package com.maguo.loan.cash.flow.job.jh;

import com.jinghang.capital.api.dto.BankChannel;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import com.maguo.loan.cash.flow.job.JobParam;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @version 1.0
 * @title:
 * @desc:
 * @date 2025/8/16 21:13
 */
@SpringBootTest
class ReccJhBaiweiJobTest {
    @Autowired
    private ReccJhBaiweiJob reccJhBaiweiJob;

    @Test
    void doJob() {
        JobParam jobParam = new JobParam();
        jobParam.setChannel(FlowChannel.FQLQY001.name());
        jobParam.setBankChannel(BankChannel.CYBK);
        jobParam.setStartDate(LocalDate.of(2025,7,9));
        reccJhBaiweiJob.doJob(jobParam);
    }
}
