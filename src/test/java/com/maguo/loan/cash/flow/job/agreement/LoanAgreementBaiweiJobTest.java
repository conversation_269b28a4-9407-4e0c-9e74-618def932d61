package com.maguo.loan.cash.flow.job.agreement;

import com.maguo.loan.cash.flow.job.JobParam;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @version 1.0
 * @title:
 * @desc:
 * @date 2025/8/17 12:09
 */
@SpringBootTest
class LoanAgreementBaiweiJobTest {

    @Autowired
    private LoanAgreementBaiweiJob loanAgreementBaiweiJob;

    @Test
    void doJob() {
        JobParam jobParam = new JobParam();
        jobParam.setStartDate(LocalDate.of(2025, 8, 4));
        loanAgreementBaiweiJob.doJob(jobParam);
    }
}
