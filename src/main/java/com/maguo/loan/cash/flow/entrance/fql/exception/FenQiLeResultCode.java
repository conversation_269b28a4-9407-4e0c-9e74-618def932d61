package com.maguo.loan.cash.flow.entrance.fql.exception;

/**
 * @ClassName ResultCode
 * <AUTHOR>
 * @Description 公共响应码
 * @Date 2025/8/06 14:27
 * @Version v1.0
 **/
public enum FenQiLeResultCode {

    SUCCESS("001", "请求成功"),
    FAIL("002", "请求失败"),
    SYSTEM_ERROR("99999", "系统异常"),
    SIGN_VERIFY_FAIL("10001", "验签失败"),
    INVALID_PARAM("10002", "参数错误"),
    OVERALL_SCORE_IS_INSUFFICIENT("10011", "综合评分不足"),
    REPEAT_SUBMIT("10003", "重复提交"),
    ALREADY_AN_ORDER_IN_TRANSIT("10004", "存在在途订单"),
    ORDER_ALREADY_REJECT("10005", "订单已拒绝"),
    PRE_ORDER_NOT_EXIST("10006", "订单不存在"),
    CAPITAL_LOAN_NO_CAN_NOT_BE_NULL("10007", "[capitalLoanNo]不能为空"),
    WITHHOLD_SERIAL_NO_CAN_NOT_BE_NULL("10008", "[withholdSerialNo]不能为空"),
    PARTNER_CODE_CAN_NOT_BE_NULL("10009", "[partnerCode]不能为空"),
    PARTNER_USERID_CAN_NOT_BE_NULL("10011", "[partnerUserId]不能为空"),
    PARTNER_ORDERNO_CAN_NOT_BE_NULL("10012", "[partnerOrderNo]不能为空"),
    REPAYLIST_CAN_NOT_BE_NULL("10013", "[repayList]不能为空"),
    REPAY_LIST_CAN_NOT_BE_NULL("10013", "[repay_list]不能为空"),
    PERIOD_ERROR("10014", "还款期数错误"),
    REPAY_PURPOSE_ERROR("10015", "还款类型错误"),
    REPAY_PURPOSE_ERROR_NOT_SUPPORTED("10016", "还款类型错误,不支持该还款类型"),
    DUPLICATE_BIND_CARD("10020", "您已经绑定%s，请勿重复绑卡"),
    BIND_CARD_RECORD_NOT_EXIST("10021", "绑卡记录不存在"),
    CREDIT_NOT_EXIST_SUCCEED("10022", "不存在成功的授信记录"),
    BIND_CARD_ERROR("10023", "绑卡失败"),
    VERIFY_CODE_EXPIRED("10024", "验证码已失效"),
    LOAN_NOT_EXIST("10030", "借款记录不存在"),
    REPAY_NOT_SUPPORTED_LOAN_DATE("10040", "放款日当天不允许发起还款"),
    REPAY_NOT_SUPPORTED_REPAY_TIME("10041", "当前时间段不支持发起还款"),
    REPAY_PLAN_ALREADY_SUCCESS("10042", "本期还款已经成功"),
    REPAY_TRIAL_ERROR("10043", "还款试算失败"),
    REPAY_PLAN_NORMAL_NOT_EXIST("10044", "未查询到待还记录"),
    CREDIT_FAIL_IN_THIRTY_DAY("10010", "30天内授信失败"),
    CREDIT_AGE_LIMIT("10045", "授信年龄限制"),
    USER_ID_AREADY_EXISTS("10046", "用户ID已经存在"),
    WITHHOLD_AMT_CAN_NOT_BE_NULL("10051", "代扣总金额不能为空"),
    WITHHOLD_DETAIL_CAN_NOT_BE_NULL("10052", "代扣明细不能为空"),
    WITHHOLD_AMT_RPYTOTAL_AMT_INCONSISTENT("10053", "代扣总金额与代扣明细总金额不一致"),
    RPY_TYPE_NOT_SUPPORTED("10054", "还款类型不支持"),
    BILL_ID_CAN_NOT_BE_NULL("10055", "还款请求流水号不能为空"),
    APPLY_ID_CAN_NOT_BE_NULL("10056", "贷款申请编号不能为空"),
    REPAY_AMOUNT_CAN_NOT_BE_NULL("10057", "还款总额不能为空"),
    APPLY_ID_CALLBACK_CAN_NOT_BE_NULL("10058", "[applyId]不能为空"),
    LOAN_STATUS_CAN_NOT_BE_NULL("10059", "放款结果不能为空或放款结果参数错误"),
    RISK_ORDER_CAN_NOT_BE_NULL("10060", "风控记录不存在"),
    QUERY_DATA_WITHOUT("0000", "订单数据不存在");


    private String code;
    private String msg;

    FenQiLeResultCode(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public FenQiLeResultCode getByCode(String code) {
        for (FenQiLeResultCode fenQiLeResultCode : FenQiLeResultCode.values()) {
            if (fenQiLeResultCode.getCode().equals(code)) {
                return fenQiLeResultCode;
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }
}
