package com.maguo.loan.cash.flow.service;


import com.jinghang.capital.api.dto.BankChannel;
import com.jinghang.common.util.StringUtil;
import com.maguo.loan.cash.flow.common.BizException;
import com.maguo.loan.cash.flow.common.ResultCode;
import com.maguo.loan.cash.flow.entity.CapitalConfig;
import com.maguo.loan.cash.flow.entity.Credit;
import com.maguo.loan.cash.flow.entity.FlowConfig;
import com.maguo.loan.cash.flow.entity.FlowRouteConfig;
import com.maguo.loan.cash.flow.entity.Loan;
import com.maguo.loan.cash.flow.entity.Order;
import com.maguo.loan.cash.flow.entity.OrderRouterRecord;
import com.maguo.loan.cash.flow.entity.PreOrder;
import com.maguo.loan.cash.flow.entity.RouterCheckRecord;
import com.maguo.loan.cash.flow.enums.AbleStatus;
import com.maguo.loan.cash.flow.enums.AmountType;
import com.maguo.loan.cash.flow.enums.FlowCapitalEnable;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import com.maguo.loan.cash.flow.enums.OrderState;
import com.maguo.loan.cash.flow.enums.ProcessState;
import com.maguo.loan.cash.flow.enums.RouteState;
import com.maguo.loan.cash.flow.enums.WhetherState;
import com.maguo.loan.cash.flow.repository.CapitalConfigRepository;
import com.maguo.loan.cash.flow.repository.CreditRepository;
import com.maguo.loan.cash.flow.repository.FlowConfigRepository;
import com.maguo.loan.cash.flow.repository.FlowRouteConfigRepository;
import com.maguo.loan.cash.flow.repository.LoanRecordRepository;
import com.maguo.loan.cash.flow.repository.LoanRepository;
import com.maguo.loan.cash.flow.repository.OrderRepository;
import com.maguo.loan.cash.flow.repository.OrderRouterRecordRepository;
import com.maguo.loan.cash.flow.repository.PreOrderRepository;
import com.maguo.loan.cash.flow.repository.RouterCheckRecordRepository;
import com.maguo.loan.cash.flow.service.event.CreditResultEvent;
import com.maguo.loan.cash.flow.service.event.LoanResultEvent;
import com.maguo.loan.cash.flow.util.AmountUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/12/20
 */
@Service
public class OrderRouterService {
    private static final Logger logger = LoggerFactory.getLogger(OrderRouterService.class);

    private CreditService creditService;

    private CreditRepository creditRepository;

    private OrderRepository orderRepository;

    private OrderRouterRecordRepository orderRouterRecordRepository;
    private RouterCheckRecordRepository routerCheckRecordRepository;

    private ApplicationEventPublisher eventPublisher;

    private WarningService warningService;

    private FlowRouteConfigRepository flowRouteConfigRepository;

    private FlowConfigRepository flowConfigRepository;

    private CapitalConfigRepository capitalConfigRepository;

    @Autowired
    private PreOrderRepository preOrderRepository;

    @Autowired
    private MqService mqService;

    private static final int ID_NUMBER_LENGTH_18 = 18;
    private static final int ID_NUMBER_LENGTH_15 = 15;
    private static final String DATE_FORMAT = "yyyyMMdd";
    private static final int BIRTH_DATE_START_INDEX_18 = 6;
    private static final int BIRTH_DATE_END_INDEX_18 = 14;
    private static final int BIRTH_DATE_START_INDEX_15 = 6;
    private static final int BIRTH_DATE_END_INDEX_15 = 12;
    private static final String CENTURY_PREFIX = "19";
    private LoanRepository loanRepository;
    private LoanRecordRepository loanRecordRepository;

    private static final List<RouteState> NEED_REFRESH_STATE = List.of(RouteState.SUSPEND, RouteState.WAIT);

    /**
     * 订单路由
     *
     * @param orderId
     */
    public void router(String orderId) {
        Order order = orderRepository.findById(orderId).orElseThrow(() -> new BizException(ResultCode.ORDER_NOT_EXIST));
        if (creditRepository.existsByOrderIdAndStateIn(order.getId(), ProcessState.INIT, ProcessState.PROCESSING)) {
            warningService.warn("存在授信中的记录,请核实订单授信数据:{}" + order.getId(), logger::error);
            return;
        }
        //判断是否存在未失败的放款记录
        if (loanRecordRepository.existsByOrderIdAndLoanStateNotIn(orderId, ProcessState.FAILED)) {
            warningService.warn("非预期路由流程结束,存在未失败的放款记录:" + order.getId(), logger::error);
            return;
        }
        // 准备路由
        refreshRouters(order);
        // 遍历wait状态的路由记录
        List<OrderRouterRecord> routerRecords = orderRouterRecordRepository.findByOrderIdAndRouteStateOrderByPriority(order.getId(), RouteState.WAIT);
        for (OrderRouterRecord record : routerRecords) {
            // 不匹配资方或者流量规则，继续下一条
            if (!checkRouteRule(order, record, "fundCodes")) {
                continue;
            }
            // 路由规则通过,到对应资方去授信
            order.setBankChannel(record.getBankChannel());
            creditService.routeApply(order, record.getId());
            // 跳出遍历
            return;
        }

        List<OrderRouterRecord> allRouteRecords = orderRouterRecordRepository.findByOrderId(orderId);
        if (allRouteRecords.stream().anyMatch(rr -> ProcessState.PROCESSING == rr.getCreditState())) {
            // 存在授信处理中记录
            warningService.warn("存在授信中的记录,请核实订单授信数据:{}" + order.getId(), logger::error);
            return;
        }
        if (allRouteRecords.stream().anyMatch(rr -> RouteState.SUSPEND.equals(rr.getRouteState()))) {
            // 存在挂起的路由记录
            orderSuspend(order);
            return;
        }

        if (allRouteRecords.stream().allMatch(rr -> rr.getRouteState().isFinal())) {
            // 走完全部路由, 无资方授信处理中, 无挂起 -> 授信失败
            finalCreditFailed(order);
        }
    }

    /**
     * 路由规则检测
     *
     * @param order
     * @param record
     * @param fundCodes
     * @return
     */
    private Boolean checkRouteRule(Order order, OrderRouterRecord record, String fundCodes) {
        RouterCheckRecord checkRecord = routerCheckRecordRepository.findByRouterRecordId(record.getId())
            .orElseThrow(() -> new BizException(ResultCode.ROUTER_RECORD_CHECK_NOT_EXIST));

        if (StringUtils.contains(fundCodes, record.getBankChannel().name())) {
            failRouterByRule(record, "风控资方前筛未通过");
            return Boolean.FALSE;
        }
        if (AbleStatus.DISABLE == checkRecord.getBankEnabled()) {
            failRouterByRule(record, "资金方禁用");
            return Boolean.FALSE;
        }
        if (StringUtil.isBlank(checkRecord.getFlowRuleId())) {
            failRouterByRule(record, "流量方未配置");
            return Boolean.FALSE;
        }
        if (AbleStatus.DISABLE == checkRecord.getFlowEnabled()) {
            failRouterByRule(record, "流量方禁用");
            return Boolean.FALSE;
        }

        List<String> supportIrrLevelList = parseStringToList(checkRecord.getSupportIrrLevel());
        String approveRate = order.getApproveRate().name();
        if (!supportIrrLevelList.contains(approveRate)) {
            failRouterByRule(record, "风控费率超过资方限定费率");
            return Boolean.FALSE;
        }

        List<String> periodsList = parseStringToList(checkRecord.getBankPeriodsRange());
        if (!periodsList.contains(order.getApplyPeriods().toString())) {
            failRouterByRule(record, "申请期数超过资方限定期数");
            return Boolean.FALSE;
        }

//        int age = calculateAgeFromIdNumber(order.getCertNo());
//        if (!isAgeInRange(age, checkRecord.getBankAgesRange())) {
//            failRouterByRule(record, "年龄不在资方限定范围");
//            return Boolean.FALSE;
//        }

        if (!isAmountInRange(order.getApplyAmount(), checkRecord.getBankSingleAmtRange())) {
            failRouterByRule(record, "申请金额不在资方限定范围");
            return Boolean.FALSE;
        }

        if (!isAllowRenewed(order.getUserId(), checkRecord)) {
            failRouterByRule(record, "资方不允许续借");
            return Boolean.FALSE;
        }

        // 资方额度检查
        BigDecimal bankCreditDayLimit = checkRecord.getBankCreditDayLimit();
        BigDecimal bankCreditDayUsed = checkRecord.getBankCreditDayUsed();
        if (order.getApplyAmount().add(bankCreditDayUsed).compareTo(bankCreditDayLimit) > 0) {
            routerSuspend(record, "超过资方日授信额度上限");
            return Boolean.FALSE;
        }

        // 流量额度检查
        BigDecimal flowCreditDayAmt = checkRecord.getFlowCreditDayAmt();
        BigDecimal flowCreditDayUsed = checkRecord.getFlowCreditDayUsed();
        if (order.getApplyAmount().add(flowCreditDayUsed).compareTo(flowCreditDayAmt) > 0) {
            routerSuspend(record, "超过流量日授信额度上限");
            return Boolean.FALSE;
        }

        // 判断当前时间是否满足条件 bankCreditStartTime bankCreditEndTime 为时:分:秒，不带年月日
        LocalTime currentTime = LocalTime.now();
        LocalTime bankCreditStartTimeLocalTime = LocalTime.parse(checkRecord.getBankCreditStartTime());
        LocalTime bankCreditEndTimeLocalTime = LocalTime.parse(checkRecord.getBankCreditEndTime());
        if (currentTime.isBefore(bankCreditStartTimeLocalTime) || currentTime.isAfter(bankCreditEndTimeLocalTime)) {
            routerSuspend(record, "超过资方授信时间范围");
            return Boolean.FALSE;
        }

        // 路由通过
        record.setProjectCode("cjrd-" + (order.getApplyChannel() == null ? "lvxin" : order.getApplyChannel()) + "-cyxj");
        record.setRouteState(RouteState.SUCCEED);
        record.setCreditState(ProcessState.PROCESSING);
        orderRouterRecordRepository.save(record);
        return Boolean.TRUE;
    }

    private List<String> parseStringToList(String str) {
        if (StringUtil.isNotBlank(str)) {
            return Arrays.asList(str.split("\\s*,\\s*"));
        }
        return new ArrayList<>();
    }

    private boolean isAgeInRange(int age, String range) {
        if (StringUtil.isNotBlank(range)) {
            String[] ages = range.split("\\s*,\\s*");
            try {
                int min = Integer.parseInt(ages[0]);
                int max = Integer.parseInt(ages[1]);
                return age >= min && age <= max;
            } catch (NumberFormatException e) {
                throw new BizException(ResultCode.AGE_FORMAT_ERROR);
            }
        }
        return true;
    }

    private boolean isAmountInRange(BigDecimal amount, String range) {
        if (StringUtil.isNotBlank(range)) {
            String[] amt = range.split("\\s*,\\s*");
            try {
                BigDecimal min = new BigDecimal(amt[0]);
                BigDecimal max = new BigDecimal(amt[1]);
                return amount.compareTo(min) >= 0 && amount.compareTo(max) <= 0;
            } catch (NumberFormatException e) {
                throw new BizException(ResultCode.AMOUNT_RANGE_FORMAT_ERROR);
            }
        }
        return true;
    }


    private boolean isAllowRenewed(String userId, RouterCheckRecord checkRecord) {
        return WhetherState.N != checkRecord.getRenewedFlag()
            || !orderRepository
            .existsByUserIdAndAmountTypeNotAndBankChannelAndOrderState(userId, AmountType.REVOLVING, checkRecord.getBankChannel(), OrderState.LOAN_PASS);
    }

    public static int calculateAgeFromIdNumber(String idNumber) {
        // 检查身份证号码长度是否合法
        if (idNumber == null || idNumber.length() != ID_NUMBER_LENGTH_18 && idNumber.length() != ID_NUMBER_LENGTH_15) {
            throw new IllegalArgumentException("身份证号码长度不正确");
        }
        // 提取出生日期字符串
        String birthDateString;
        if (idNumber.length() == ID_NUMBER_LENGTH_18) {
            birthDateString = idNumber.substring(BIRTH_DATE_START_INDEX_18, BIRTH_DATE_END_INDEX_18);
        } else { // 处理15位身份证号码，年份需转换为4位
            birthDateString = CENTURY_PREFIX + idNumber.substring(BIRTH_DATE_START_INDEX_15, BIRTH_DATE_END_INDEX_15);
        }
        // 将字符串转换为LocalDate类型
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DATE_FORMAT);
        LocalDate birthDate = LocalDate.parse(birthDateString, formatter);
        // 计算年龄
        LocalDate currentDate = LocalDate.now();
        long age = ChronoUnit.YEARS.between(birthDate, currentDate);
        return (int) age;
    }

    /**
     * 不满足规则路由失败
     *
     * @param record
     * @param failReason
     */
    private void failRouterByRule(OrderRouterRecord record, String failReason) {
        record.setRouteState(RouteState.FAILED);
        record.setRouteFailReason(failReason);
        orderRouterRecordRepository.save(record);
    }

    /**
     * 路由挂起
     *
     * @param record
     * @param suspendMsg
     */
    private void routerSuspend(OrderRouterRecord record, String suspendMsg) {
        record.setRouteState(RouteState.SUSPEND);
        record.setRouteFailReason(suspendMsg);
        orderRouterRecordRepository.save(record);
    }

    /**
     * 准备路由记录
     *
     * @param order
     */
    private void refreshRouters(Order order) {

        FlowConfig flowConfig = flowConfigRepository.findByFlowChannel(order.getFlowChannel())
            .orElseThrow(() -> new BizException(ResultCode.FLOW_CONFIG_NOT_EXIST));

        // 根据flowId 查询 flow_route_config
        List<FlowRouteConfig> flowRouteConfig = flowRouteConfigRepository.findByFlowIdAndEnabled(flowConfig.getId(), FlowCapitalEnable.ENABLE);
        flowRouteConfig.sort(Comparator.comparingInt(FlowRouteConfig::getPriority));
        PreOrder preOrder = preOrderRepository.findByOrderNoAndFlowChannel(order.getOuterOrderId(), order.getFlowChannel()).orElseThrow();

        flowRouteConfig.forEach(frc -> {
            CapitalConfig capitalConfig = capitalConfigRepository.findById(frc.getCapitalId())
                .orElseThrow(() -> new BizException(ResultCode.ROURE_CAPITAL_CONFIG_NOT_EXIST));
            if (AbleStatus.DISABLE == capitalConfig.getEnabled()) {
                //资金方已禁用，不生成路由记录
                //forEach使用return代替continue
                return;
            }
            if (WhetherState.Y == preOrder.getIsAssignBankChannel() && capitalConfig.getBankChannel() != order.getBankChannel()) {
                return;
            }
            OrderRouterRecord routerRecord = orderRouterRecordRepository.findByOrderIdAndRouteConfigId(order.getId(), frc.getId())
                .orElseGet(() -> createRouterRecord(order, capitalConfig, frc));

            routerCheckRecordRepository.findByRouterRecordId(routerRecord.getId()).ifPresentOrElse(checkRecord -> {
                if (!NEED_REFRESH_STATE.contains(routerRecord.getRouteState())) {
                    return;
                }

                BigDecimal bankCreditDayUsed = obtainBankCreditDayUsed(capitalConfig.getBankChannel());
                BigDecimal flowCreditDayUsed = obtainFlowCreditDayUsed(flowConfig.getFlowChannel());

                if (RouteState.SUSPEND.equals(routerRecord.getRouteState())) {
                    LocalTime bankCreditStartTime = LocalTime.parse(capitalConfig.getCreditStartTime());
                    LocalTime bankCreditEndTime = LocalTime.parse(capitalConfig.getCreditEndTime());
                    // 满足授信时间区间
                    if (LocalTime.now().isAfter(bankCreditStartTime) && LocalTime.now().isBefore(bankCreditEndTime)) {
                        // 低于资方&流量授信限额
                        if (order.getApplyAmount().add(bankCreditDayUsed).compareTo(capitalConfig.getCreditDayLimit()) <= 0
                            && order.getApplyAmount().add(flowCreditDayUsed).compareTo(flowConfig.getCreditDayAmt()) <= 0) {
                            // 解除路由挂起
                            routerRecord.setRouteState(RouteState.WAIT);
                            routerRecord.setRemark("解除路由挂起");
                            orderRouterRecordRepository.save(routerRecord);
                        }
                    }
                }
                if (RouteState.WAIT.equals(routerRecord.getRouteState())) {
                    // 更新checkRecord
                    checkRecord.setBankCreditStartTime(capitalConfig.getCreditStartTime());
                    checkRecord.setBankCreditEndTime(capitalConfig.getCreditEndTime());
                    checkRecord.setBankCreditDayLimit(capitalConfig.getCreditDayLimit());
                    checkRecord.setFlowCreditDayAmt(flowConfig.getCreditDayAmt());
                    checkRecord.setBankCreditDayUsed(bankCreditDayUsed); // 已使用资方授信额度
                    checkRecord.setFlowCreditDayUsed(flowCreditDayUsed); // 流量已使用授信额度
                    routerCheckRecordRepository.save(checkRecord);
                }

            }, () -> createCheckRecord(routerRecord, flowConfig, capitalConfig));

        });

    }

    /**
     * 创建路由记录
     *
     * @param o
     * @param cc
     * @param frc
     * @return
     */
    private OrderRouterRecord createRouterRecord(Order o, CapitalConfig cc, FlowRouteConfig frc) {
        OrderRouterRecord newRecord = new OrderRouterRecord();
        newRecord.setOrderId(o.getId());
        newRecord.setUserId(o.getUserId());
        newRecord.setFlowChannel(o.getFlowChannel());
        newRecord.setRouteConfigId(frc.getId());
        newRecord.setPriority(frc.getPriority());
        newRecord.setBankChannel(cc.getBankChannel());
        newRecord.setRouteState(RouteState.WAIT);
        newRecord.setCreditState(ProcessState.INIT);
        return orderRouterRecordRepository.save(newRecord);
    }

    /**
     * 创建路由检测记录
     *
     * @param orr
     * @param fc
     * @param cc
     * @return
     */
    private RouterCheckRecord createCheckRecord(OrderRouterRecord orr, FlowConfig fc, CapitalConfig cc) {
        RouterCheckRecord checkRecord = new RouterCheckRecord();
        checkRecord.setRouterRecordId(orr.getId());
        checkRecord.setRouterConfigId(orr.getRouteConfigId());
        checkRecord.setBankPriority(orr.getPriority());
        checkRecord.setBankChannel(orr.getBankChannel());

        // 资方规则
        checkRecord.setSupportIrrLevel(cc.getSupportIrrLevel());
        checkRecord.setBankCreditDayLimit(cc.getCreditDayLimit());
        checkRecord.setBankLoanDayLimit(cc.getLoanDayLimit());
        checkRecord.setBankPeriodsRange(cc.getPeriodsRange());
        checkRecord.setBankAgesRange(cc.getAgesRange());
        checkRecord.setBankSingleAmtRange(cc.getSingleAmtRange());
        checkRecord.setBankEnabled(cc.getEnabled());
        checkRecord.setBankCreditStartTime(cc.getCreditStartTime());
        checkRecord.setBankCreditEndTime(cc.getCreditEndTime());
        checkRecord.setBankLoanStartTime(cc.getLoanStartTime());
        checkRecord.setBankLoanEndTime(cc.getLoanEndTime());
        // 资方授信已用额度
        checkRecord.setBankCreditDayUsed(obtainBankCreditDayUsed(cc.getBankChannel()));

        // 流量规则
        checkRecord.setFlowRuleId(fc.getId());
        checkRecord.setFlowChannel(fc.getFlowChannel());
        checkRecord.setFlowCreditDayAmt(fc.getCreditDayAmt());
        checkRecord.setFlowLoanDayAmt(fc.getLoanDayAmt());
        checkRecord.setFlowEnabled(fc.getEnabled());
        // 流量授信已用额度
        checkRecord.setFlowCreditDayUsed(obtainFlowCreditDayUsed(fc.getFlowChannel()));
        //是否可续借
        checkRecord.setRenewedFlag(cc.getRenewedFlag());

        //
        return routerCheckRecordRepository.save(checkRecord);
    }

    /**
     * 资方已用授信额度
     *
     * @param bankChannel
     * @return
     */
    public BigDecimal obtainBankCreditDayUsed(BankChannel bankChannel) {
        LocalDateTime beginTime = LocalDate.now().atStartOfDay();
        LocalDateTime endTime = LocalDate.now().plusDays(1).atStartOfDay();

        //授信成功
        BigDecimal bankCreditDayUsedSucceed = creditRepository.sumBankCreditUsedSucceed(
            bankChannel, ProcessState.SUCCEED, beginTime, endTime);
        //授信中
        BigDecimal bankCreditDayUsedProcessing = creditRepository.sumBankCreditUsedProcessing(
            bankChannel, List.of(ProcessState.INIT, ProcessState.PROCESSING), beginTime, endTime);

        return AmountUtil.sum(bankCreditDayUsedSucceed, bankCreditDayUsedProcessing);
    }

    /**
     * 流量已用授信额度
     *
     * @param flowChannel
     * @return
     */
    private BigDecimal obtainFlowCreditDayUsed(FlowChannel flowChannel) {
        BigDecimal flowCreditDayUsed = creditRepository.sumFlowCreditUsed(
            flowChannel, ProcessState.SUCCEED, LocalDate.now().atStartOfDay(), LocalDate.now().plusDays(1).atStartOfDay());

        return Objects.nonNull(flowCreditDayUsed) ? flowCreditDayUsed : BigDecimal.ZERO;
    }

    /**
     * 资方授信结果,回到此方法处理后续
     *
     * @param creditId
     */
    public void loopBack(String creditId) {
        Credit credit = creditRepository.findById(creditId).orElseThrow(() -> new BizException(ResultCode.CREDIT_NOT_FOUND));
        // 查找路由记录
        OrderRouterRecord routerRecord = orderRouterRecordRepository.findById(credit.getRouterRecordId())
            .orElseThrow(() -> new BizException(ResultCode.ROUTER_RECORD_NOT_EXIST));
        Order order = orderRepository.findById(routerRecord.getOrderId()).orElseThrow(() -> new BizException(ResultCode.ORDER_NOT_EXIST));
        switch (credit.getState()) {
            case FAILED -> {
                routerRecord.setCreditState(ProcessState.FAILED);
                routerRecord.setCreditFailReason(credit.getFailReason());
                orderRouterRecordRepository.save(routerRecord);
                if (FlowChannel.FQLQY001.equals(order.getFlowChannel())){
                    //资方 授信失败 通知百维
                    mqService.submitBaiWeiRiskNotify(order.getRiskId());
                }
                // 继续路由
                router(order.getId());
            }
            case SUCCEED -> afterCreditSucceed(order, routerRecord, credit);
            default -> {
            }
        }
    }

    /**
     * 资方授信成功后
     *
     * @param order
     */
    private void afterCreditSucceed(Order order, OrderRouterRecord routerRecord, Credit credit) {
        // 路由记录
        routerRecord.setCreditState(ProcessState.SUCCEED);
        orderRouterRecordRepository.save(routerRecord);
        // 订单状态
        logger.info("授信成功,路由结束:{}", order.getId());
        order.setBankChannel(credit.getBankChannel());
        order.setOrderState(OrderState.CREDITING.equals(order.getOrderState()) ? OrderState.CREDIT_PASS : order.getOrderState());
        orderRepository.save(order);

        // 授信成功事件
        eventPublisher.publishEvent(new CreditResultEvent(credit.getId(), order.getId()));
    }

    /**
     * 路由结束，无成功，无挂起。最终失败
     *
     * @param order
     */
    private void finalCreditFailed(Order order) {
        //判断是否存在放款失败记录,如果存在,则将订单和借据置为失败
        Loan loan = loanRepository.findByOrderId(order.getId());
        if (Objects.nonNull(loan)) {
            if (ProcessState.PROCESSING != loan.getLoanState()) {
                warningService.warn("非预期路由流程结束,请核实借据数据:" + loan.getId(), logger::error);
                return;
            }

            //creditRepository.findTopByOrderIdOrderByCreatedTimeDesc(order.getId()).ifPresent(credit -> {
            //    loan.setCreditId();
            //});


            //已路由所有资方,授信失败,将借据和订单置为放款失败
            loan.setLoanState(ProcessState.FAILED);
            loan = loanRepository.save(loan);

            //推送放款失败事件
            eventPublisher.publishEvent(new LoanResultEvent(loan.getId(), loan.getLoanState(), loan.getLoanTime(), loan.getBankChannel(),
                loan.getFailReason()));
            return;
        }


        if (creditRepository.existsByOrderIdAndStateNotIn(order.getId(), ProcessState.FAILED)) {
            warningService.warn("非预期路由流程结束,请核实该订单数据:" + order.getId(), msg -> logger.error(msg));
            return;
        }

        // 授信失败
        logger.info("路由结束,最终授信失败:{}", order.getId());
        order.setOrderState(OrderState.CREDIT_FAIL);
        order.setRemark("路由结束,授信失败");
        orderRepository.save(order);
        if (FlowChannel.FQLQY001.equals(order.getFlowChannel())){
            //路由结束 未走到资方授信-授信失败 通知百维
            mqService.submitBaiWeiRiskNotify(order.getRiskId());
        }
        // 授信失败事件
        eventPublisher.publishEvent(new CreditResultEvent(null, order.getId()));
    }

    /**
     * 将所有等待的路由记录置为取消
     */
    public void cancelOrderRouter(String orderId, String cancelReason) {
        List<OrderRouterRecord> otherRecords = orderRouterRecordRepository.findByOrderIdAndRouteStateIn(orderId, RouteState.WAIT, RouteState.SUSPEND);
        otherRecords.forEach(r -> {
            r.setRouteFailReason(cancelReason);
            r.setRouteState(RouteState.CANCEL);
        });
        orderRouterRecordRepository.saveAll(otherRecords);
    }

    /**
     * 订单挂起
     *
     * @param order
     */
    private void orderSuspend(Order order) {
        // 订单挂起, 存在挂起的路由记录
        logger.info("订单挂起, 存在挂起的路由记录:{}", order.getId());
        order.setOrderState(OrderState.SUSPENDED);
        order.setRemark("订单挂起, 存在挂起的路由记录");
        orderRepository.save(order);
    }

    @Autowired
    public void setCreditService(CreditService creditService) {
        this.creditService = creditService;
    }

    @Autowired
    public void setOrderRepository(OrderRepository orderRepository) {
        this.orderRepository = orderRepository;
    }

    @Autowired
    public void setOrderRouterRecordRepository(OrderRouterRecordRepository orderRouterRecordRepository) {
        this.orderRouterRecordRepository = orderRouterRecordRepository;
    }

    @Autowired
    public void setRouterCheckRecordRepository(RouterCheckRecordRepository routerCheckRecordRepository) {
        this.routerCheckRecordRepository = routerCheckRecordRepository;
    }

    @Autowired
    public void setCreditRepository(CreditRepository creditRepository) {
        this.creditRepository = creditRepository;
    }

    @Autowired
    public void setEventPublisher(ApplicationEventPublisher eventPublisher) {
        this.eventPublisher = eventPublisher;
    }

    @Autowired
    public void setWarningService(WarningService warningService) {
        this.warningService = warningService;
    }

    @Autowired
    public void setFlowRouteConfigRepository(FlowRouteConfigRepository flowRouteConfigRepository) {
        this.flowRouteConfigRepository = flowRouteConfigRepository;
    }

    @Autowired
    public void setFlowConfigRepository(FlowConfigRepository flowConfigRepository) {
        this.flowConfigRepository = flowConfigRepository;
    }

    @Autowired
    public void setCapitalConfigRepository(CapitalConfigRepository capitalConfigRepository) {
        this.capitalConfigRepository = capitalConfigRepository;
    }


    @Autowired
    public void setLoanRepository(LoanRepository loanRepository) {
        this.loanRepository = loanRepository;
    }

    @Autowired
    public void setLoanRecordRepository(LoanRecordRepository loanRecordRepository) {
        this.loanRecordRepository = loanRecordRepository;
    }
}
