package com.maguo.loan.cash.flow.repository;

import com.maguo.loan.cash.flow.entity.UserRiskRecordExternal;
import com.maguo.loan.cash.flow.enums.ApplyType;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import com.maguo.loan.cash.flow.enums.RiskChannel;
import org.springframework.data.jpa.repository.JpaRepository;

/**
 * 外部风控
 *
 * <AUTHOR>
 * @date 2025/8/18
 */
public interface UserRiskRecordExternalRepository extends JpaRepository<UserRiskRecordExternal, String> {

    UserRiskRecordExternal findTopByUserIdAndFlowChannelAndApplyChannelAndApplyTypeAndRiskChannelOrderByCreatedTimeDesc(String userId, FlowChannel flowChannel,
                                                                                                                        String applyChannel, ApplyType applyType,
                                                                                                                        RiskChannel riskChannel);

    UserRiskRecordExternal findTopByUserIdOrderByCreatedTimeDesc(String userId);
}
