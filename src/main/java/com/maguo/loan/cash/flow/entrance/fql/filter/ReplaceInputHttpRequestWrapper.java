package com.maguo.loan.cash.flow.entrance.fql.filter;

import jakarta.servlet.ReadListener;
import jakarta.servlet.ServletInputStream;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletRequestWrapper;

import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStreamReader;

public class ReplaceInputHttpRequestWrapper extends HttpServletRequestWrapper {

    private final ByteArrayInputStream inputStream;

    private ServletInputStream servletInputStream;
    private BufferedReader reader;
    private final int contentLength;

    public ReplaceInputHttpRequestWrapper(HttpServletRequest request, byte[] bytes) {
        super(request);
        contentLength = bytes.length;
        inputStream = new ByteArrayInputStream(bytes);

    }

    @Override
    public ServletInputStream getInputStream() throws IOException {
        if (servletInputStream == null) {
            this.servletInputStream = new CacheServletInputStream(getRequest().getInputStream());
        }
        return this.servletInputStream;
    }

    @Override
    public BufferedReader getReader() throws IOException {
        if (this.reader == null) {
            this.reader = new BufferedReader(new InputStreamReader(getInputStream(), getCharacterEncoding()));
        }
        return this.reader;
    }


    @Override
    public int getContentLength() {
        return contentLength;
    }

    @Override
    public long getContentLengthLong() {
        return contentLength;
    }

    private class CacheServletInputStream extends ServletInputStream {

        private final ServletInputStream is;

        CacheServletInputStream(ServletInputStream is) {
            this.is = is;
        }

        @Override
        public boolean isFinished() {
            return is.isFinished();
        }

        @Override
        public boolean isReady() {
            return is.isReady();
        }

        @Override
        public void setReadListener(ReadListener readListener) {
            is.setReadListener(readListener);
        }

        @Override
        public int read() throws IOException {
            return inputStream.read();
        }
    }
}
