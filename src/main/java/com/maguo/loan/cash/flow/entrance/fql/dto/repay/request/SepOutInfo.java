package com.maguo.loan.cash.flow.entrance.fql.dto.repay.request;

import java.math.BigDecimal;

/**
 * 出账信息
 * [
 * {
 * "amt": “出账金额（单位 分”,
 * "type": "出账主体"(1: 用户账户出账 , 2.补差账户出账(即营销金额),
 * "account": "出账账户"
 * }
 * ]
 * 备注：补差账户若金额为0，会将信息透传给资方，但资方无需将金额为0的补差账户信息透传给宝付或通联
 */
public class SepOutInfo {
    /**
     * 出账金额（单位 分）
     */
    private BigDecimal amt;
    /**
     * 出账主体"(1: 用户账户出账 , 2.补差账户出账(即营销金额))"
     */
    private String type;
    /**
     * 出账账户
     */
    private String account;

    public BigDecimal getAmt() {
        return amt;
    }

    public void setAmt(BigDecimal amt) {
        this.amt = amt;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getAccount() {
        return account;
    }

    public void setAccount(String account) {
        this.account = account;
    }
}
