package com.maguo.loan.cash.flow.entrance.fql.dto.loan;


/*
 * @Description: 分期乐放款申请响应
 * @Author: abai
 * @Date: 2025-8-11 下午 02:38
 */
public class LoanApplyResponse {

    /**
     * 放款结果,0-成功，1-失败，2-查无此单，99-处理中
     */
    private Integer loanStatus;

    /**
     * 放款结果描述
     */
    private String msg;

    public Integer getLoanStatus() {
        return loanStatus;
    }

    public void setLoanStatus(Integer loanStatus) {
        this.loanStatus = loanStatus;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }


    public static LoanApplyResponse success() {
        LoanApplyResponse result = new LoanApplyResponse();
        result.setLoanStatus(0);
        result.setMsg("请求成功");
        return result;
    }

    public static LoanApplyResponse fail() {
        LoanApplyResponse result = new LoanApplyResponse();
        result.setLoanStatus(1);
        result.setMsg("请求失败");
        return result;
    }

    public static LoanApplyResponse fail(String msg) {
        LoanApplyResponse result = new LoanApplyResponse();
        result.setLoanStatus(1);
        result.setMsg(msg);
        return result;
    }

    public static LoanApplyResponse processing() {
        LoanApplyResponse result = new LoanApplyResponse();
        result.setLoanStatus(99);
        result.setMsg("放款处理中");
        return result;
    }

    public static LoanApplyResponse unknowOrder(String msg) {
        LoanApplyResponse result = new LoanApplyResponse();
        result.setLoanStatus(2);
        result.setMsg(msg);
        return result;
    }
}
