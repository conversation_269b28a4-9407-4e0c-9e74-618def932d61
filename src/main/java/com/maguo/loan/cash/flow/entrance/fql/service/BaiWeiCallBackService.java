package com.maguo.loan.cash.flow.entrance.fql.service;

import com.jinghang.common.util.JsonUtil;
import com.jinghang.common.util.StringUtil;
import com.maguo.loan.cash.flow.entity.Loan;
import com.maguo.loan.cash.flow.entity.Order;
import com.maguo.loan.cash.flow.entity.PreOrder;
import com.maguo.loan.cash.flow.entity.UserRiskRecordExternal;
import com.maguo.loan.cash.flow.entrance.common.exception.CommonApiBizException;
import com.maguo.loan.cash.flow.entrance.common.exception.CommonApiResultCode;
import com.maguo.loan.cash.flow.entrance.fql.dto.callBack.BaiWeiDisburseRiskResultNoticeRequest;
import com.maguo.loan.cash.flow.entrance.fql.dto.callBack.BaiWeiInvestorsAuditResultQueryRequest;
import com.maguo.loan.cash.flow.entrance.fql.dto.callBack.BaiWeiInvestorsAuditResultQueryResponse;
import com.maguo.loan.cash.flow.entrance.fql.dto.credit.CreditResultNoticeRequest;
import com.maguo.loan.cash.flow.entrance.fql.dto.credit.CreditResultNoticeResponse;
import com.maguo.loan.cash.flow.entrance.fql.dto.credit.InvestorsCreditResultNoticeRequest;
import com.maguo.loan.cash.flow.entrance.fql.dto.credit.InvestorsCreditResultNoticeResponse;
import com.maguo.loan.cash.flow.entrance.fql.exception.FenQiLeBizException;
import com.maguo.loan.cash.flow.entrance.fql.exception.FenQiLeResultCode;
import com.maguo.loan.cash.flow.enums.ApplyType;
import com.maguo.loan.cash.flow.enums.AuditState;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import com.maguo.loan.cash.flow.enums.OrderState;
import com.maguo.loan.cash.flow.enums.RiskChannel;
import com.maguo.loan.cash.flow.repository.LoanRepository;
import com.maguo.loan.cash.flow.repository.PreOrderRepository;
import com.maguo.loan.cash.flow.repository.UserRiskRecordExternalRepository;
import com.maguo.loan.cash.flow.repository.UserRiskRecordRepository;
import com.maguo.loan.cash.flow.service.OrderService;
import com.maguo.loan.cash.flow.service.event.RiskResultEvent;
import com.maguo.loan.cash.flow.service.event.RiskResultOutEvent;
import groovy.util.logging.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @Description 百维回调接口 业务服务类
 * @Date 2024/8/15 14:40
 * @Version v1.0
 **/
@Slf4j
@Service
public class BaiWeiCallBackService {

    private static final Logger logger = LoggerFactory.getLogger(BaiWeiCallBackService.class);

    @Autowired
    private OrderService orderService;
    @Autowired
    private UserRiskRecordRepository userRiskRecordRepository;
    @Autowired
    private UserRiskRecordExternalRepository userRiskRecordExternalRepository;
    @Autowired
    private LoanRepository loanRepository;
    @Autowired
    private PreOrderRepository preOrderRepository;
    @Autowired
    private ApplicationEventPublisher eventPublisher;
    /**
     * 支用风控审核结果通知
     *
     * @param request 请求参数
     */
    public void disburseRiskResultNotice(BaiWeiDisburseRiskResultNoticeRequest request) {
        logger.info("支用风控审核结果通知回调接口请求参数：{}", JsonUtil.toJsonString(request));
        try {
            // 用信审批申请编号
            String applyId = request.getApplyId();
            if (StringUtil.isBlank(applyId)) {
                throw new FenQiLeBizException(FenQiLeResultCode.APPLY_ID_CALLBACK_CAN_NOT_BE_NULL);
            }
            // 合作方代码
            String partnerCode = request.getPartnerCode();
            if (StringUtil.isBlank(partnerCode)) {
                throw new FenQiLeBizException(FenQiLeResultCode.PARTNER_CODE_CAN_NOT_BE_NULL);
            }
            // 放款结果 0:放款审核通过 1:放款审核拒绝
            Integer loanStatus = request.getLoanStatus();
            if (null == loanStatus || (0 != loanStatus && 1 != loanStatus)) {
                throw new FenQiLeBizException(FenQiLeResultCode.LOAN_STATUS_CAN_NOT_BE_NULL);
            }
            // 查询订单数据
            Order order = orderService.query(applyId, null, FlowChannel.FQLQY001);
            Optional.ofNullable(order).orElseThrow(() -> new CommonApiBizException(CommonApiResultCode.ORDER_NOT_EXIST));
            // 查询风控订单数据信息
            /*UserRiskRecord userRiskRecord = userRiskRecordRepository.findByUserIdAndFlowChannelAndApplyChannelAndApplyType(order.getUserId(),
                order.getFlowChannel(), order.getApplyChannel(), ApplyType.RISK_LOAN).orElseThrow();*/
            UserRiskRecordExternal userRiskRecord = userRiskRecordExternalRepository
                .findTopByUserIdAndFlowChannelAndApplyChannelAndApplyTypeAndRiskChannelOrderByCreatedTimeDesc(order.getUserId(),
                order.getFlowChannel(), order.getApplyChannel(), ApplyType.RISK_LOAN, RiskChannel.BW);
            if (null == userRiskRecord) {
                throw new FenQiLeBizException(FenQiLeResultCode.RISK_ORDER_CAN_NOT_BE_NULL);
            }
            AuditState auditState = convertToAuditState(loanStatus);
            userRiskRecord.setApproveResult(auditState);
            userRiskRecord.setRiskFinalResult(request.getMsg());
            // userRiskRecordRepository.save(userRiskRecord);
            userRiskRecordExternalRepository.save(userRiskRecord);
            if (userRiskRecord.getApproveResult() == AuditState.PASS) {
                //推送风控通过事件 - 路由资金方推送授信放款
                orderService.orderRoute(order);
            } else if (userRiskRecord.getApproveResult() == AuditState.REJECT) {
                order.setOrderState(OrderState.LOAN_FAIL);
                order.setRemark("支用风控拒绝");
                orderService.update(order);
            }
        } catch (Exception e) {
            logger.error("支用风控审核结果通知回调处理异常:{},{}", JsonUtil.toJsonString(request), e.getMessage());
        }
    }

    private AuditState convertToAuditState(Integer loanStatus) {
        return switch (loanStatus) {
            case 0 -> AuditState.PASS;
            case 1 -> AuditState.REJECT;
            default -> null;
        };
    }

    /**
     * 资方放款申请风控审核结果查询
     *
     * @param request 请求参数
     * @return 返回结果
     */
    public BaiWeiInvestorsAuditResultQueryResponse auditResultQuery(BaiWeiInvestorsAuditResultQueryRequest request) {
        logger.info("资方支用风控审核结果查询回调接口请求参数：{}", JsonUtil.toJsonString(request));
        try {
            // 用信审批申请编号
            String applyId = request.getApplyId();
            if (StringUtil.isBlank(applyId)) {
                throw new FenQiLeBizException(FenQiLeResultCode.APPLY_ID_CALLBACK_CAN_NOT_BE_NULL);
            }
            // 合作方代码
            String partnerCode = request.getPartnerCode();
            if (StringUtil.isBlank(partnerCode)) {
                throw new FenQiLeBizException(FenQiLeResultCode.PARTNER_CODE_CAN_NOT_BE_NULL);
            }
            // 查询订单数据
            Order order = orderService.query(applyId, null, FlowChannel.FQLQY001);
            if (null == order) {
                return BaiWeiInvestorsAuditResultQueryResponse.returnResult(null, 2, "查无此单");
            }
            // 订单状态为授信失败、放款通过、放款失败时返回放款审核通过或放款审核拒绝
            if (order.getOrderState() == OrderState.CREDIT_FAIL || order.getOrderState() == OrderState.LOAN_PASS
                || order.getOrderState() == OrderState.LOAN_FAIL) {
                // 放款通过则查询loan表数据
                if (order.getOrderState() == OrderState.LOAN_PASS) {
                    Loan loan = loanRepository.findByOrderId(order.getId());
                    return BaiWeiInvestorsAuditResultQueryResponse.returnResult(loan.getLoanNo(), 0, "放款审核通过");
                } else {
                    // 放款失败、授信失败则返回放款审核拒绝
                    return BaiWeiInvestorsAuditResultQueryResponse.returnResult("", 1, "放款审核拒绝");
                }
            } else {
                return BaiWeiInvestorsAuditResultQueryResponse.returnResult("", 99, "处理中");
            }
        } catch (Exception e) {
            logger.error("资方支用风控审核结果查询回调处理异常:{},{}", JsonUtil.toJsonString(request), e.getMessage());
            return BaiWeiInvestorsAuditResultQueryResponse.fail("资方支用风控审核结果查询回调处理异常");
        }
    }

    /**
     * 查询授信申请风控的结果
     *
     * @param
     * @return CreditQueryResponse
     */
    public CreditResultNoticeResponse CreditResultNotice(CreditResultNoticeRequest creditResultNoticeRequest) {
        try {
            //查询预订单
            String orderNo = creditResultNoticeRequest.getApplyId();
            PreOrder preOrder = preOrderRepository.findByOrderNoAndFlowChannel(orderNo,FlowChannel.FQLQY001).orElse(null);
            //PreOrder preOrder = preOrderRepository.findByOrderNoAndFlowChannel(orderNo,FlowChannel.valueOf("FQLQY001")).orElse(null);
            if (preOrder == null) {
                logger.error("查询授信结果找不到订单,applyId:{}", creditResultNoticeRequest.getApplyId());
                return CreditResultNoticeResponse.unknowOrder("查询不到订单");
            }
            UserRiskRecordExternal userRiskRecordExternal = userRiskRecordExternalRepository.findById(preOrder.getRiskId()).orElseThrow();
            userRiskRecordExternal.setRiskFinalResult(String.valueOf(creditResultNoticeRequest.getStatus()));
            userRiskRecordExternal.setRemark(creditResultNoticeRequest.getMsg());
            if (creditResultNoticeRequest.getStatus() == 0) {
                userRiskRecordExternal.setApproveResult(AuditState.PASS);
                //推送风控通过事件
                eventPublisher.publishEvent(new RiskResultOutEvent(userRiskRecordExternal.getId()));

            }
            if (creditResultNoticeRequest.getStatus() == 1) {
                userRiskRecordExternal.setApproveResult(AuditState.REJECT);
                //推送风控拒绝事件
                eventPublisher.publishEvent(new RiskResultOutEvent(userRiskRecordExternal.getId()));
            }
            UserRiskRecordExternal save = userRiskRecordExternalRepository.save(userRiskRecordExternal);
            //TODO 加密工具类验证
            if (ObjectUtils.isNotEmpty(save)){
//                CreditResultNoticeResponse creditResultNoticeResponse = new CreditResultNoticeResponse();
//                creditResultNoticeResponse.setMsg("1111111");
//                creditResultNoticeResponse.setStatus(1);
//                EncryptUtils encryptUtils = new EncryptUtils(fqlConfig);
//                FqlCommonRequest fqlCommonRequest = encryptUtils.encryptUtils(creditResultNoticeResponse);
//                String request = JSON.toJSONString(fqlCommonRequest);
//                System.out.println("-------------------加密验证-----------------------"+request);
                return CreditResultNoticeResponse.selectSucces("落库操作成功");
            }
            return CreditResultNoticeResponse.selectSucces("落库操作失败");
        } catch (Exception e) {
            logger.error("授信风控申请查询失败,applyId:{}" + creditResultNoticeRequest.getApplyId(), e);
            return CreditResultNoticeResponse.fail("查询失败，系统异常");
        }
    }

    /**
     * 查询资方授信申请的结果
     *
     * @param
     * @return CreditQueryResponse
     */
    public InvestorsCreditResultNoticeResponse InvestorsCreditResultNotice(InvestorsCreditResultNoticeRequest investorsCreditResultNoticeRequest) {
        try {
            //查询预订单
            String orderNo = investorsCreditResultNoticeRequest.getApplyId();
            PreOrder preOrder = preOrderRepository.findByOrderNoAndFlowChannel(orderNo, FlowChannel.FQLQY001).orElse(null);
            if (preOrder == null) {
                logger.error("查询授信结果找不到订单,applyId:{}", investorsCreditResultNoticeRequest.getApplyId());
                return InvestorsCreditResultNoticeResponse.unknowOrder("查询不到订单");
            }
            //UserRiskRecord userRiskRecord = userRiskRecordRepository.findById(preOrder.getRiskId()).orElseThrow();
            UserRiskRecordExternal userRiskRecordExternal = userRiskRecordExternalRepository.findById(preOrder.getRiskId()).orElseThrow();
            if (!ObjectUtils.isEmpty(userRiskRecordExternal)){
                switch (preOrder.getPreOrderState()) {
                    case INIT, AUDITING -> {
                        return InvestorsCreditResultNoticeResponse.processing();// 审核中
                    }
                    case AUDIT_PASS -> {
                        Order order = orderService.findByRiskId(preOrder.getRiskId());
                        if (Objects.isNull(order)) {
                            //风控通过,可能订单还没保存
                            return InvestorsCreditResultNoticeResponse.processing();// 审核中
                        }

                        if (OrderState.LOAN_CANCEL == order.getOrderState()) {
                            return InvestorsCreditResultNoticeResponse.fail();// 授信过期
                        }
                        return InvestorsCreditResultNoticeResponse.success();
                    }
                    case AUDIT_REJECT -> {
                        if (preOrder.getRemark().equals("授信累计额度已达项目限额上限")) {
                            return InvestorsCreditResultNoticeResponse.fail(preOrder.getRemark());
                        } else {
                            return InvestorsCreditResultNoticeResponse.fail(preOrder.getRemark());
                        }
                    }
                    default -> {
                    }
                }
            }

            return InvestorsCreditResultNoticeResponse.processing();
        } catch (Exception e) {
            logger.error("分期乐授信申请查询失败,applyId:{}" + investorsCreditResultNoticeRequest.getApplyId(), e);
            return InvestorsCreditResultNoticeResponse.fail("查询失败，系统异常");
        }
    }

}
