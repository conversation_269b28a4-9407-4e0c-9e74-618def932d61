package com.maguo.loan.cash.flow.entrance.fql.dto;

/**
 * @ClassName EncryptData
 * <AUTHOR>
 * @Description 分期乐 解密前请求参数
 * @Date 2025/8/6 15:36
 * @Version v1.0
 **/
public class FenQiLeEncryptData {

    /**
     * 分期乐分配的合作方代码
     */
    private String partnerCode;
    /**
     * 请求发送时间
     */
    private String timestamp;
    /**
     * 对称加密后的消息数据(业务数据)
     */
    private String bizContent;
    /**
     * 签名字符串
     */
    private String sign;

    public String getPartnerCode() {
        return partnerCode;
    }

    public void setPartnerCode(String partnerCode) {
        this.partnerCode = partnerCode;
    }

    public String getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(String timestamp) {
        this.timestamp = timestamp;
    }

    public String getBizContent() {
        return bizContent;
    }

    public void setBizContent(String bizContent) {
        this.bizContent = bizContent;
    }

    public String getSign() {
        return sign;
    }

    public void setSign(String sign) {
        this.sign = sign;
    }

}
