package com.maguo.loan.cash.flow.entrance.fql.dto.repay.response;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Description (代扣明细)(list结构) 响应参数
 * @Date 2025/08/08 16:51
 * @Version v1.0
 **/
public class WithholdDetailResponse {

    /**
     * 贷款申请编号
     * 分期乐资产号,每笔借款唯一
     */
    private String assetId;
    /**
     * 资金方放款编号/借据号
     * 资金方订单唯一标识
     */
    private String capitalLoanNo;
    /**
     * 订单维度的实还总额,保留两位有效数字
     * 单笔订单代扣的总额(单位:元)
     */
    private BigDecimal rpyTotalAmt;
    /**
     * 还款类型 0-待还、1-正常还款、2-部分提前还、 3-逾期还款 、4-全部提前还 、5-坏账代偿、 6-回购
     */
    private Integer rpyType;
    /**
     * 代扣日期
     * 用户实还日，用户主动发起是当前日；定时扣款是应还日，格式=yyyy-MM-dd
     */
    private String rpyDate;
    /**
     * 还款账单明细
     */
    private List<RpyDetailsResponse> rpyDetails;

    public String getAssetId() {
        return assetId;
    }

    public void setAssetId(String assetId) {
        this.assetId = assetId;
    }

    public String getCapitalLoanNo() {
        return capitalLoanNo;
    }

    public void setCapitalLoanNo(String capitalLoanNo) {
        this.capitalLoanNo = capitalLoanNo;
    }

    public BigDecimal getRpyTotalAmt() {
        return rpyTotalAmt;
    }

    public void setRpyTotalAmt(BigDecimal rpyTotalAmt) {
        this.rpyTotalAmt = rpyTotalAmt;
    }

    public Integer getRpyType() {
        return rpyType;
    }

    public void setRpyType(Integer rpyType) {
        this.rpyType = rpyType;
    }

    public String getRpyDate() {
        return rpyDate;
    }

    public void setRpyDate(String rpyDate) {
        this.rpyDate = rpyDate;
    }

    public List<RpyDetailsResponse> getRpyDetails() {
        return rpyDetails;
    }

    public void setRpyDetails(List<RpyDetailsResponse> rpyDetails) {
        this.rpyDetails = rpyDetails;
    }
}
