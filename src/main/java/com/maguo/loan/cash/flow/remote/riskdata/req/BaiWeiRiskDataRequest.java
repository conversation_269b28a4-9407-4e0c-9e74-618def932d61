package com.maguo.loan.cash.flow.remote.riskdata.req;

import java.math.BigDecimal;

/**
 * @author: z<PERSON><PERSON><PERSON>
 * @date: 2025/8/18 14:57
 */
public class BaiWeiRiskDataRequest {

    /**
     * 用信审批申请编号
     */
    private String applyId;
    /**
     * 合作方代码
     */
    private String partnerCode;

    /**
     * 资方授信编号
     */
    private String creditNo;

    /**
     * 资产类型
     */
    private Integer orderType;
    /**
     * 借款金额
     */
    private BigDecimal loanPrincipal;

    /**
     * 借款期数
     */
    private Integer loanTerm;

    /**
     * 年化利率
     */
    private BigDecimal annualRate;

    /**
     * 申请贷款用途
     */
    private String loanUse;

    /**
     * 手机号
     */
    private String mobileNo;
    /**
     * 借款人收款户名
     */
    private String debitAccountName;

    /**
     * 收款人银行卡开户行
     */
    private String debitOpenAccountBank;

    /**
     * 收款人银行卡卡号
     */
    private String debitAccountNo;

    /**
     * 开户行编码
     */
    private String paymentBankCode;

    /**
     * 收款卡联行号
     */
    private String debitCnaps;

    /**
     * 还款方式
     */
    private Integer repayType;

    /**
     * 扩展信息
     */
    private String extend;

    public String getApplyId() {
        return applyId;
    }

    public void setApplyId(String applyId) {
        this.applyId = applyId;
    }

    public String getPartnerCode() {
        return partnerCode;
    }

    public void setPartnerCode(String partnerCode) {
        this.partnerCode = partnerCode;
    }

    public String getCreditNo() {
        return creditNo;
    }

    public void setCreditNo(String creditNo) {
        this.creditNo = creditNo;
    }

    public Integer getOrderType() {
        return orderType;
    }

    public void setOrderType(Integer orderType) {
        this.orderType = orderType;
    }

    public BigDecimal getLoanPrincipal() {
        return loanPrincipal;
    }

    public void setLoanPrincipal(BigDecimal loanPrincipal) {
        this.loanPrincipal = loanPrincipal;
    }

    public Integer getLoanTerm() {
        return loanTerm;
    }

    public void setLoanTerm(Integer loanTerm) {
        this.loanTerm = loanTerm;
    }

    public BigDecimal getAnnualRate() {
        return annualRate;
    }

    public void setAnnualRate(BigDecimal annualRate) {
        this.annualRate = annualRate;
    }

    public String getLoanUse() {
        return loanUse;
    }

    public void setLoanUse(String loanUse) {
        this.loanUse = loanUse;
    }

    public String getMobileNo() {
        return mobileNo;
    }

    public void setMobileNo(String mobileNo) {
        this.mobileNo = mobileNo;
    }

    public String getDebitAccountName() {
        return debitAccountName;
    }

    public void setDebitAccountName(String debitAccountName) {
        this.debitAccountName = debitAccountName;
    }

    public String getDebitOpenAccountBank() {
        return debitOpenAccountBank;
    }

    public void setDebitOpenAccountBank(String debitOpenAccountBank) {
        this.debitOpenAccountBank = debitOpenAccountBank;
    }

    public String getDebitAccountNo() {
        return debitAccountNo;
    }

    public void setDebitAccountNo(String debitAccountNo) {
        this.debitAccountNo = debitAccountNo;
    }

    public String getPaymentBankCode() {
        return paymentBankCode;
    }

    public void setPaymentBankCode(String paymentBankCode) {
        this.paymentBankCode = paymentBankCode;
    }

    public String getDebitCnaps() {
        return debitCnaps;
    }

    public void setDebitCnaps(String debitCnaps) {
        this.debitCnaps = debitCnaps;
    }

    public Integer getRepayType() {
        return repayType;
    }

    public void setRepayType(Integer repayType) {
        this.repayType = repayType;
    }

    public String getExtend() {
        return extend;
    }

    public void setExtend(String extend) {
        this.extend = extend;
    }
}
