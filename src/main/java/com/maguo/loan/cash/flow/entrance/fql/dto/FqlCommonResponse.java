package com.maguo.loan.cash.flow.entrance.fql.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.maguo.loan.cash.flow.entrance.lvxin.dto.LvxinResponse;
import com.maguo.loan.cash.flow.entrance.lvxin.exception.LvxinResultCode;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/12/27 16:42
 **/
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FqlCommonResponse implements Serializable {
    @Serial
    private static final long serialVersionUID = -3296521925086928295L;

    /**
     * 业务返回码
     */
    private String code;

    /**
     * 业务返回描述
     */
    private String msg;
    /**
     * 响应发送时间
     */
    private String timestamp;
    /**
     * 业务数据
     */
    private String bizContent;
    /**
     * 签名数据
     */
    private String sign;

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public String getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(String timestamp) {
        this.timestamp = timestamp;
    }

    public String getBizContent() {
        return bizContent;
    }

    public void setBizContent(String bizContent) {
        this.bizContent = bizContent;
    }

    public String getSign() {
        return sign;
    }

    public void setSign(String sign) {
        this.sign = sign;
    }

    public static LvxinResponse exception(String msg) {
        LvxinResponse result = new LvxinResponse();
        result.setCode(LvxinResultCode.SYSTEM_ERROR.getCode());
        result.setMessage(msg);
        return result;
    }
}
