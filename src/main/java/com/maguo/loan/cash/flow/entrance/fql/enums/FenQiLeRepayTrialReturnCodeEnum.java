package com.maguo.loan.cash.flow.entrance.fql.enums;

/**
 * <AUTHOR>
 * @Description 还款试算返回状态码
 * @Date 2025/8/6 15:20
 * @Version v1.0
 **/
public enum FenQiLeRepayTrialReturnCodeEnum {

    /**
     * 0-成功；
     * 1-失败；
     * 2-异常；
     * 3-查无此单；
     * 4-已结清
     */
    SUCCESS("0", "成功"),
    FAIL("1", "失败"),
    ABNORMAL("2", "异常"),
    NO_SUCH_ORDER_FOUND("3", "查无此单"),
    SETTLED("4", "已结清");

    private String code;

    private String desc;

    FenQiLeRepayTrialReturnCodeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
