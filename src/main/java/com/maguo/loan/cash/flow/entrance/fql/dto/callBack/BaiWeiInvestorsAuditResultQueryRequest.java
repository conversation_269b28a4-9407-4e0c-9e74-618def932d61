package com.maguo.loan.cash.flow.entrance.fql.dto.callBack;

import jakarta.validation.constraints.NotBlank;

/**
 * @Description 查询资方放款申请风控审核结果 请求参数
 * @Date 2025/08/18 09:51
 * @Version v1.0
 **/
public class BaiWeiInvestorsAuditResultQueryRequest {

    /**
     * 用信审批申请编号
     */
    @NotBlank(message = "用信审批申请编号不能为空")
    private String applyId;
    /**
     * 合作方代码
     */
    @NotBlank(message = "合作方代码不能为空")
    private String partnerCode;

    public String getApplyId() {
        return applyId;
    }

    public void setApplyId(String applyId) {
        this.applyId = applyId;
    }

    public String getPartnerCode() {
        return partnerCode;
    }

    public void setPartnerCode(String partnerCode) {
        this.partnerCode = partnerCode;
    }
}
