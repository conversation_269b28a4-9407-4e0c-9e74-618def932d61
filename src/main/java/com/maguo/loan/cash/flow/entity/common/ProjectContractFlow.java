package com.maguo.loan.cash.flow.entity.common;

import com.maguo.loan.cash.flow.enums.AbleStatus;
import com.maguo.loan.cash.flow.enums.FileType;
import com.maguo.loan.cash.flow.enums.LoanStage;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 合同模板配置表-资产方
 *
 * @Author: Lior
 * @CreateTime: 2025/8/18 11:55
 */
@Getter
@Setter
@Entity
@Table(name = "project_contract_flow")
public class ProjectContractFlow implements Serializable {

    @Serial
    private static final long serialVersionUID = 1234567890123456789L;

    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 项目唯一编码
     */
    @Column(name = "project_code")
    private String projectCode;

    /**
     * 合同模板唯一编码
     */
    @Column(name = "template_code")
    private String templateCode;

    /**
     * 合同英文简称(文件名)
     */
    @Column(name = "contract_file_name")
    private String contractFileName;

    /**
     * 合同模板中文描述
     */
    @Column(name = "contract_description")
    private String contractDescription;

    /**
     * 合同签署阶段
     */
    @Column(name = "loan_stage")
    @Enumerated(EnumType.STRING)
    private LoanStage loanStage;

    /**
     * 模板归属方
     */
    @Column(name = "template_owner")
    private String templateOwner;

    /**
     * 是否融担签章
     */
    @Column(name = "is_rd_signature")
    private String isRdSignature;

    /**
     * 签章类型
     */
    @Column(name = "seal_type")
    private String sealType;

    /**
     * 是否回传流量方
     */
    @Column(name = "is_return_to_flow")
    private String isReturnToFlow;

    /**
     * 模板备注
     */
    private String remark;

    /**
     * 启用状态
     */
    @Enumerated(EnumType.STRING)
    private AbleStatus enabled;

    /**
     * 创建人
     */
    @Column(name = "create_by")
    private String createBy;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    @Column(name = "update_by")
    private String updateBy;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private LocalDateTime updateTime;

    /**
     * 合同协议编码
     */
    @Column(name = "agreement_code")
    private String agreementCode;

    /**
     * 文件类型
     */
    @Enumerated(EnumType.STRING)
    private FileType fileType;

    /**
     * 文件扩展名
     */
    @Column(name = "extension")
    private String extension;

    /**
     * 是否需要重新签署
     */
    @Column(name = "need_sign_again", columnDefinition = "boolean default false")
    private Boolean needSignAgain = false;
}
