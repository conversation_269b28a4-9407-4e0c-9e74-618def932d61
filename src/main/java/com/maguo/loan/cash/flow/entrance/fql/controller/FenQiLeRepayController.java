package com.maguo.loan.cash.flow.entrance.fql.controller;

import com.maguo.loan.cash.flow.entrance.fql.dto.repay.FenQiLeRealReturnNoticeRequest;
import com.maguo.loan.cash.flow.entrance.fql.dto.repay.FenQiLeRealReturnNoticeResponse;
import com.maguo.loan.cash.flow.entrance.fql.dto.repay.FenQiLeRealReturnResultRequest;
import com.maguo.loan.cash.flow.entrance.fql.dto.repay.FenQiLeRealReturnResultResponse;
import com.maguo.loan.cash.flow.entrance.fql.dto.repay.FenQiLeRepayRequest;
import com.maguo.loan.cash.flow.entrance.fql.dto.repay.FenQiLeRepayResponse;
import com.maguo.loan.cash.flow.entrance.fql.dto.repay.FenQiLeRepayResultRequest;
import com.maguo.loan.cash.flow.entrance.fql.dto.repay.FenQiLeRepayResultResponse;
import com.maguo.loan.cash.flow.entrance.fql.dto.repay.FenQiLeRepayTrialRequest;
import com.maguo.loan.cash.flow.entrance.fql.dto.repay.FenQiLeRepayTrialResponse;
import com.maguo.loan.cash.flow.entrance.fql.service.FenQiLeService;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/fenQiLe/api")
public class FenQiLeRepayController extends FqlApiValidator {

    private static final Logger logger = LoggerFactory.getLogger(FenQiLeRepayController.class);

    @Autowired
    private FenQiLeService fenQiLeService;

    /**
     * 还款试算接口
     *
     * @param request 请求参数
     * @return 返回数据
     */
    @PostMapping("/advanceRepayTrialCal")
    public FenQiLeRepayTrialResponse repayTrial(@RequestBody @Valid FenQiLeRepayTrialRequest request, BindingResult bindingResult) {
        try {
            // 必填参数校验
            validate(bindingResult);
            // 业务逻辑
            return fenQiLeService.trail(request);
        } catch (Exception e) {
            logger.error("分期乐还款试算失败", e);
            return FenQiLeRepayTrialResponse.fail("还款试算失败，系统异常");
        }
    }

    /**
     * 代扣还款申请
     *
     * @param request 请求参数
     * @return 返回数据
     */
    @PostMapping("/doRepay")
    public FenQiLeRepayResponse doRepay(@RequestBody @Valid FenQiLeRepayRequest request, BindingResult bindingResult) {
        try {
            // 必填参数校验
            validate(bindingResult);
            // 业务逻辑
            return fenQiLeService.repay(request);
        } catch (Exception e) {
            logger.error("分期乐代扣还款申请失败", e);
            return FenQiLeRepayResponse.fail("代扣还款申请失败，系统异常");
        }
    }

    /**
     * 代扣结果查询
     *
     * @param request 请求参数
     * @return 返回结果
     */
    @PostMapping("/repayQuery")
    public FenQiLeRepayResultResponse repayQuery(@RequestBody @Valid FenQiLeRepayResultRequest request, BindingResult bindingResult) {
        try {
            // 必填参数校验
            validate(bindingResult);
            // 业务逻辑
            return fenQiLeService.repayQuery(request);
        } catch (Exception e) {
            logger.error("分期乐代扣结果查询失败", e);
            return FenQiLeRepayResultResponse.fail("代扣结果查询失败，系统异常");
        }
    }

    /**
     * 实还通知接口
     *
     * @param request 请求参数
     * @return 返回结果
     */
    @PostMapping("/repayNotify")
    public FenQiLeRealReturnNoticeResponse repayNotify(@RequestBody @Valid FenQiLeRealReturnNoticeRequest request, BindingResult bindingResult) {
        try {
            // 必填参数校验
            validate(bindingResult);
            // 业务逻辑
            return fenQiLeService.repayNotify(request);
        } catch (Exception e) {
            logger.error("分期乐实还通知失败", e);
            return FenQiLeRealReturnNoticeResponse.fail("实还通知失败，系统异常");
        }
    }

    /**
     * 实还通知查询接口
     *
     * @param request 请求参数
     * @return 返回结果
     */
    @PostMapping("/repayNotifyQuery")
    public FenQiLeRealReturnResultResponse repayNotifyQuery(@RequestBody @Valid FenQiLeRealReturnResultRequest request, BindingResult bindingResult) {
        try {
            // 必填参数校验
            validate(bindingResult);
            // 业务逻辑
            return fenQiLeService.repayNotifyQuery(request);
        } catch (Exception e) {
            logger.error("分期乐实还通知查询失败", e);
            return FenQiLeRealReturnResultResponse.fail("实还通知查询失败，系统异常");
        }
    }

}
