package com.maguo.loan.cash.flow.entrance.fql.enums;

/**
 * <AUTHOR>
 * @Description 实还通知还款状态
 * @Date 2025/8/11 15:20
 * @Version v1.0
 **/
public enum FenQiLeRepayStatusEnum {

    SUCCESS(1, "成功(结算成功)"),
    FAIL(2, "失败(结算失败)"),
    PROCESSING(3, "还款中(结算处理中)"),
    DOES_NOT_EXIST(4, "查无此通知（乐信侧会重复发起还款）");

    private Integer code;

    private String desc;

    FenQiLeRepayStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

}
