package com.maguo.loan.cash.flow.entrance.fql.exception;

import com.maguo.loan.cash.flow.common.BizException;
import com.maguo.loan.cash.flow.common.ResultCode;
import com.maguo.loan.cash.flow.entrance.fql.dto.FenQiLeResponse;
import com.maguo.loan.cash.flow.service.WarningService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

@RestControllerAdvice(basePackages = "com.maguo.loan.cash.flow.entrance.fql.controller")
public class FenQiLeGlobalExceptionHandler {

    private static final Logger logger = LoggerFactory.getLogger(FenQiLeGlobalExceptionHandler.class);

    @Autowired
    private WarningService warningService;

    @ExceptionHandler(value = FenQiLeBizException.class)
    public FenQiLeResponse bizHandler(FenQiLeBizException e) {
        logger.error("分期乐业务异常", e);
        return FenQiLeResponse.fail(e.getMessage());
    }

    @ExceptionHandler(value = BizException.class)
    public FenQiLeResponse bizHandler(BizException e) {
        logger.error("分期乐业务异常:", e);
        if (e.getResultCode().getWarning()) {
            warningService.warn("分期乐业务异常:" + e.getMessage());
        }
        return FenQiLeResponse.fail(e.getMessage());
    }


    @ExceptionHandler(value = Exception.class)
    public FenQiLeResponse sysHandler(Exception e) {
        warningService.warn("分期乐系统异常:" + e.getMessage(), msg -> logger.error("分期乐系统异常:", e));
        return FenQiLeResponse.fail(ResultCode.SYS_ERROR.getMsg());
    }

}
