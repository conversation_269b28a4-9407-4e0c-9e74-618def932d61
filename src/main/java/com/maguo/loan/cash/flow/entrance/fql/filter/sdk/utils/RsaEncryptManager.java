package com.maguo.loan.cash.flow.entrance.fql.filter.sdk.utils;


import com.maguo.loan.cash.flow.entrance.fql.filter.sdk.LxException;
import com.maguo.loan.cash.flow.entrance.fql.filter.sdk.enums.KeyFactoryEnum;

import javax.crypto.Cipher;
import javax.xml.bind.DatatypeConverter;
import java.io.BufferedReader;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileReader;
import java.security.KeyFactory;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.interfaces.RSAPrivateKey;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;

/**
 * <AUTHOR>
 * @Description  rsa加密
 * @Date 2020/4/23 14:45
 */
public class RsaEncryptManager {

    private KeyFactoryEnum keyFactoryEnum = KeyFactoryEnum.RSA;

    /**
     * <AUTHOR>
     * @Description   RSA加密接口
     * @Param encryptString 待加密字符串,  keyPath 公钥所在路径或者解析好的公钥字符串
     * @Date 2020/5/19 17:39
     */
    public  String encrypyt(String encryptString,String keyPath) {
        try {
            PublicKey publicKey = getPubKey(keyPath);
            Cipher cipher = Cipher.getInstance("RSA");
            byte[] data = encryptString.getBytes("utf-8");
            // 编码前设定编码方式及密钥
            cipher.init(Cipher.ENCRYPT_MODE, publicKey);
            int keyBit = getKeySize(publicKey);
            int inputLen = data.length;
            ByteArrayOutputStream out = new ByteArrayOutputStream();
            int offSet = 0;
            int step = keyBit / 8 - 11;

            for (int i = 0; inputLen - offSet > 0; offSet = i * step) {
                byte[] cache;
                if (inputLen - offSet > step) {
                    cache = cipher.doFinal(data, offSet, step);
                } else {
                    cache = cipher.doFinal(data, offSet, inputLen - offSet);
                }

                out.write(cache, 0, cache.length);
                ++i;
            }

            byte[] encryptedData = out.toByteArray();
            out.close();
            return byteToBase64(encryptedData);
        } catch (Exception e) {
            throw new LxException("加密失败: " + e.getMessage());
        }

    }

    /**
     * <AUTHOR>
     * @Description  解密接口
     * @Param decryptString 待解密字符串,  keyPath 私钥所在路径或者解析好的私钥字符串
     * @Date 2020/5/19 17:39
     */
    public  String decryption(String decryptString,String keyPath) {
        try {
            PrivateKey privateKey = getPriKey(keyPath);
            Cipher cipher = Cipher.getInstance("RSA");
            byte[] data = base64ToByte(decryptString);
            cipher.init(Cipher.DECRYPT_MODE, privateKey);
            int keyBit = getKeySize(privateKey);
            int inputLen = data.length;
            ByteArrayOutputStream out = new ByteArrayOutputStream();
            int offSet = 0;
            int step = keyBit / 8;

            for (int i = 0; inputLen - offSet > 0; offSet = i * step) {
                byte[] cache;
                if (inputLen - offSet > step) {
                    cache = cipher.doFinal(data, offSet, step);
                } else {
                    cache = cipher.doFinal(data, offSet, inputLen - offSet);
                }

                out.write(cache, 0, cache.length);
                ++i;
            }

            byte[] decryptedData = out.toByteArray();
            out.close();
            return new String(decryptedData);
        } catch (Exception e) {
            System.out.println(e);
            throw new LxException("解密失败: " + e.getMessage());
        }
    }



    //获取公钥长度
    private  int getKeySize(PublicKey publicKey) {
        RSAPublicKey rsaPublicKey = (RSAPublicKey) publicKey;
        return rsaPublicKey.getModulus().bitLength();
    }

    //获取私钥长度
    private  int getKeySize(PrivateKey privateKey) {
        RSAPrivateKey rsaPrivateKey = (RSAPrivateKey) privateKey;
        return rsaPrivateKey.getModulus().bitLength();
    }

    /**
     * 通过字符串生成公钥
     */
    public  PublicKey getPubKey(String pubPath) throws LxException {
//        if(StringUtils.isBlank(pubPath)){
//            throw new TestException("pubPath" );
//        }
        File file = new File(pubPath);
        String certData = pubPath;
        if(file.isFile()){
            certData = getCertDataByFilePath(pubPath);
        }
        try{
            PublicKey publicKey = null;
            byte[] decodeKey = base64ToByte(certData);

            X509EncodedKeySpec x509 = new X509EncodedKeySpec(decodeKey);
            KeyFactory keyFactory = KeyFactory.getInstance(keyFactoryEnum.getKey());
            publicKey = keyFactory.generatePublic(x509);
            return publicKey;
        }catch (Exception e){
           // log.error("getPubkey失败异常",e);
            throw new LxException("getPubkey失败异常" );
        }
    }


    /**
     * java 使用的是pkcs8格式的私钥，一般都是pkcs1的，所以需要将其转换为pkcs8格式的
     * 该函数接受的是pkcs1格式的私钥文件（PEM）格式
     * @param priPath 有可能是文件路径也有可能是字符串
     * @return
     * @throws Exception
     *
     */
    private  PrivateKey getPriKey(String priPath) throws LxException {
//        if(org.apache.commons.lang.StringUtils.isBlank(priPath)){
//            throw new LxException("priPath为空" );
//        }
        File file = new File(priPath);
        String certData = priPath;
        if(file.isFile()){
            certData = getCertDataByFilePath(priPath);
        }

        try{
            byte[] keyBytes = base64ToByte(certData);
            PKCS8EncodedKeySpec pkcs8KeySpec = new PKCS8EncodedKeySpec(keyBytes);
            KeyFactory keyFactory = KeyFactory.getInstance(keyFactoryEnum.getKey());
            return keyFactory.generatePrivate(pkcs8KeySpec);
        }catch (Exception e){
          //  log.error("getPrikey失败异常",e);
            throw new LxException("getPrikey失败" );
        }

    }


    //根据文件路径读取证书数据
    private String getCertDataByFilePath(String filePath){
        try(BufferedReader br = new BufferedReader(new FileReader(filePath));){
            String s = br.readLine();
            StringBuilder str = new StringBuilder();
            s = br.readLine();
            while (s.charAt(0) != '-'){
                str.append(s + "\r");
                s = br.readLine();
            }
            return s;
        }catch (Exception e){
         //  log.info("失败异常",e);
            throw new LxException("根据文件路径读取证书数据失败" );
        }
    }
    private  String byteToBase64(byte[] data)  {
        return DatatypeConverter.printBase64Binary(data);
    }

    private  byte[] base64ToByte(String data) {
        return DatatypeConverter.parseBase64Binary(data);
    }





}
