package com.maguo.loan.cash.flow.entrance.fql.filter;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.alibaba.fastjson2.JSONObject;
import com.jinghang.common.util.JsonUtil;
import com.maguo.loan.cash.flow.entrance.fql.config.BaiWeiConfig;
import com.maguo.loan.cash.flow.entrance.fql.config.FqlConfig;
import com.maguo.loan.cash.flow.entrance.fql.dto.FqlCommonRequest;
import com.maguo.loan.cash.flow.entrance.fql.dto.FqlCommonResponse;
import com.maguo.loan.cash.flow.entrance.fql.exception.FqlBizException;
import com.maguo.loan.cash.flow.entrance.fql.exception.FqlResultCode;
import com.maguo.loan.cash.flow.entrance.fql.filter.sdk.LxClient;

import com.maguo.loan.cash.flow.entrance.ppd.common.BizException;
import com.maguo.loan.cash.flow.entrance.ppd.common.ValidationException;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpFilter;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.util.ContentCachingResponseWrapper;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Date;

public class EncryptFilter extends HttpFilter {

    private static final Logger logger = LoggerFactory.getLogger(EncryptFilter.class);

    private FqlConfig config;

//    private BaiWeiConfig baiWeiConfig;

    public EncryptFilter(FqlConfig fqlConfig) {
        this.config = fqlConfig;
    }

//    public EncryptFilter(BaiWeiConfig baiWeiConfig) {
//        this.baiWeiConfig = baiWeiConfig;
//    }

    static String partnerCode = "";
    static String bizContent = "";
    static String timestamp = "";
    static String sign = "";

    //分期乐公钥(加密解密用)
    private static String PublicKey = "";

    //分期乐公钥(加签解签用)
    private static String SignPublicKey = "";

    //合作方私钥(加密解密用)
    private static  String partnerPrivateKey = "";

    //合作方私钥(加签解签用)
    private static  String partnerSignPrivateKey = "";



    @Override
    protected void doFilter(HttpServletRequest req, HttpServletResponse res, FilterChain chain) throws IOException {
        String requestStr = IOUtils.toString(req.getInputStream(), StandardCharsets.UTF_8);
        logger.info("分期乐 入参原始报文: {}, 请求地址: {}, 跳过验签: {}", requestStr, req.getRequestURL(), config.getSkipSignVerify());
        LxClient lxClient = new LxClient();
        String decrptString = new String();
        byte[] contentBytes = null;
        boolean aTrue = false;
        StringBuffer requestURL = req.getRequestURL();
        //判断分期乐还是百维
        if (requestURL.toString().contains("fenQiLe")){
            //验签
            SignPublicKey = config.getLxSignPublicKey();
            //解密
            partnerPrivateKey = config.getPartnerPrivateKey();
            //加密
            PublicKey = config.getLxPublicKey();
            //加签
            partnerSignPrivateKey = config.getPartnerSignPrivateKey();
        }else if (requestURL.toString().contains("baiwei")){
            //验签
            SignPublicKey = config.getBwSignPublicKey();
            //解密
            partnerPrivateKey = config.getPartnerPrivateKey2();
            //加密
            PublicKey = config.getBwPublicKey();
            //加签
            partnerSignPrivateKey = config.getPartnerSignPrivateKey2();
        }else {

        }

        try {
            FqlCommonRequest requestData = JsonUtil.convertToObject(requestStr, FqlCommonRequest.class);
            partnerCode = requestData.getPartnerCode();
            bizContent = requestData.getBizContent();
            timestamp = requestData.getTimestamp();
            sign = requestData.getSign();
            // 兼容测试
            aTrue = StringUtils.equals("true", requestData.getSign());
            if (!config.getSkipSignVerify() && !aTrue) {
                //检查参数有效性
                check(requestData);

                System.out.println("资金方获取的字符串:" + requestStr);
                //分期乐请求合作方
                //反解析成对象
                com.alibaba.fastjson.JSONObject request = JSON.parseObject(requestStr);
                String sign = request.getString("sign");
                request.remove("sign");
                String data = JSON.toJSONString(request, SerializerFeature.MapSortField);
                //验签需改动 TODO
                //boolean checkSign = lxClient.verifySign(data, sign, config.getLxSignPublicKey());
                boolean checkSign = lxClient.verifySign(data, sign, SignPublicKey);
                if (!checkSign) {
                    logger.error("{} 验签失败,{}", partnerCode, req.getRequestURL());
                    throw new FqlBizException(FqlResultCode.SIGN_VERIFY_FAIL);
                }
                try {
                    //业务数据解密
//                    String decrypted = PpdRsaUtil.decryptRequest(requestData.getBizContent(), config.getServerPrivateKey());
//                    requestData.setBizContent(decrypted);
                    //验签通过之后,解密文件  需改动 TODO
                    //decrptString = lxClient.decrypt(request.getString("bizContent"), config.getPartnerPrivateKey());
                    decrptString = lxClient.decrypt(request.getString("bizContent"), partnerPrivateKey);
                    logger.info("{} 入参解密后报文: {},{}", partnerCode, decrptString, req.getRequestURL());
                } catch (Exception e) {
                    logger.error("{} 解密失败,{}", partnerCode, e);
                    throw new FqlBizException(FqlResultCode.SIGN_VERIFY_FAIL);
                }
            }else {
                decrptString = bizContent;
            }

            ReplaceInputHttpRequestWrapper requestWrapper = new ReplaceInputHttpRequestWrapper(req, decrptString.getBytes(StandardCharsets.UTF_8));
            ContentCachingResponseWrapper responseWrapper = new ContentCachingResponseWrapper(res);
            chain.doFilter(requestWrapper, responseWrapper);

            res.setCharacterEncoding("utf-8");
            res.setContentType("application/json");

            contentBytes = responseWrapper.getContentAsByteArray();
            logger.info("{} 出参原始报文: {}, {}", partnerCode, new String(contentBytes, StandardCharsets.UTF_8), req.getRequestURL());
            String content = new String(contentBytes, StandardCharsets.UTF_8);

            FqlCommonResponse response = assembleSuccessResponse(content);

            if (!config.getSkipSignVerify() && !aTrue) {
                //没有业务返回数据就不需要加签、加密
                if (contentBytes.length > 0) {

                    //String bizContent = JSON.toJSONString(response.getBizContent());

                    //响应data加密、加签
                    //先对请求数据进行加密 需改动 TODO
                    //String encrptString = lxClient.encrypt(response.getBizContent(), config.getLxPublicKey());
                    String encrptString = lxClient.encrypt(response.getBizContent(), PublicKey);
                    response.setBizContent(encrptString);

                    //对整个请求对象进行加签 需改动 TODO
                    String data = JSON.toJSONString(response, SerializerFeature.MapSortField);
                    //String sign = lxClient.sign(data, config.getPartnerSignPrivateKey());
                    String sign = lxClient.sign(data, partnerSignPrivateKey);
                    //对请求数据进行加签
                    response.setSign(sign);
                }
            }

            String responseStr = JSONObject.toJSONString(response);

            logger.info("{} 出参加密后报文: {}", partnerCode, responseStr);
            contentBytes = responseStr.getBytes(StandardCharsets.UTF_8);

            res.setContentLength(contentBytes.length);
            ServletOutputStream outputStream = res.getOutputStream();
            outputStream.write(contentBytes);
            outputStream.flush();

        } catch (Exception e) {
            logger.error("{} 调用异常,{}", partnerCode, req.getRequestURL(), e);
            res.setCharacterEncoding("utf-8");
            res.setContentType("application/json");
            FqlCommonResponse response = assembleFailResponse(e);

            if (!config.getSkipSignVerify() && !aTrue) {
                //没有业务返回数据就不需要加签、加密
                if (contentBytes.length > 0) {

                    //String bizContent = JSON.toJSONString(response.getBizContent());

                    //响应data加密、加签
                    //先对请求数据进行加密 需改动 TODO
                    //String encrptString = lxClient.encrypt(response.getBizContent(), config.getLxPublicKey());
                    String encrptString = lxClient.encrypt(response.getBizContent(), PublicKey);
                    response.setBizContent(encrptString);

                    //对整个请求对象进行加签 需改动 TODO
                    String data = JSON.toJSONString(response, SerializerFeature.MapSortField);
                    //String sign = lxClient.sign(data, config.getPartnerSignPrivateKey());
                    String sign = lxClient.sign(data, partnerSignPrivateKey);
                    //对请求数据进行加签
                    response.setSign(sign);
                }

                String responseStr = JSONObject.toJSONString(response);
                logger.info("{} 出参加密后报文: {}", partnerCode, responseStr);

                contentBytes = JsonUtil.toJsonString(response).getBytes(StandardCharsets.UTF_8);
                res.setContentLength(contentBytes.length);
                ServletOutputStream outputStream = res.getOutputStream();
                outputStream.write(contentBytes);
                outputStream.flush();
            }
        }
    }

    private static FqlCommonResponse assembleFailResponse(Exception e) {
        FqlCommonResponse response = new FqlCommonResponse();
        String code = FqlResultCode.FAILURE.getCode();
        String msg = e.getMessage();
        response.setBizContent("");

        if (e.getCause() instanceof RuntimeException) {
            code = FqlResultCode.SYSTEM_ERROR.getCode();
            msg = FqlResultCode.SYSTEM_ERROR.getMsg();
        }

        if (e.getCause() instanceof BizException) {
            BizException ex = (BizException) e.getCause();
            code = FqlResultCode.SYSTEM_ERROR.getCode();
            msg = ex.getMessage();
        }

        if (e.getCause() instanceof ValidationException) {
            ValidationException ex = (ValidationException) e.getCause();
            code = FqlResultCode.SYSTEM_ERROR.getCode();
            msg = ex.getMessage();
        }

        if (e.getCause() instanceof FqlBizException) {
            FqlBizException ex = (FqlBizException) e.getCause();
            code = FqlResultCode.SYSTEM_ERROR.getCode();
            msg = ex.getMessage();
        }
        response.setSign(sign);
        response.setCode(code);
        response.setMsg(msg);
        response.setTimestamp(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        return response;
    }
    private static FqlCommonResponse assembleSuccessResponse(String content) {
        FqlCommonResponse response = new FqlCommonResponse();
        response.setTimestamp(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        response.setCode(FqlResultCode.SUCCESS.getCode());
        response.setMsg(FqlResultCode.SUCCESS.getMsg());
        response.setBizContent(content);
        return response;
    }

    private void check(FqlCommonRequest requestData) {
        Arrays.stream(FlowChannel.values()).filter(c -> c.name().equals(requestData.getPartnerCode())).findAny().orElseThrow(
            () -> new FqlBizException(FqlResultCode.NOT_SUPPORT_CHANNEL));
    }
}
