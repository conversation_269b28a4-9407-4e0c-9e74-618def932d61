package com.maguo.loan.cash.flow.entrance.fql.enums;

/**
 * <AUTHOR>
 * @Description 拍拍贷 订单状态
 * @Date 2024/12/30 16:42
 **/
public enum FqlOrderStatus {

    SUCCESS("00", "成功") {
        @Override
        public boolean isFinal() {
            return true;
        }
    },

    FAIL("01", "失败") {
        @Override
        public boolean isFinal() {
            return true;
        }
    },
    PROCESSING("99", "处理中"),
    SYS_ERROR("10", "系统异常");

    private String code;
    private String msg;


    public boolean isFinal() {
        return false;
    }

    FqlOrderStatus(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public static FqlOrderStatus getByCode(String code) {
        for (FqlOrderStatus value : FqlOrderStatus.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }
}
