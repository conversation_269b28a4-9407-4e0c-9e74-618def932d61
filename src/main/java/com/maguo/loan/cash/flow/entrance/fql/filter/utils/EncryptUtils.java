package com.maguo.loan.cash.flow.entrance.fql.filter.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.maguo.loan.cash.flow.entrance.fql.config.BaiWeiConfig;
import com.maguo.loan.cash.flow.entrance.fql.config.FqlConfig;
import com.maguo.loan.cash.flow.entrance.fql.dto.FqlCommonRequest;
import com.maguo.loan.cash.flow.entrance.fql.dto.FqlCommonResponse;
import com.maguo.loan.cash.flow.entrance.fql.exception.FqlBizException;
import com.maguo.loan.cash.flow.entrance.fql.exception.FqlResultCode;
import com.maguo.loan.cash.flow.entrance.fql.filter.sdk.LxClient;
import jakarta.servlet.http.HttpFilter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;


public class EncryptUtils extends HttpFilter {

//    private BaiWeiConfig baiWeiConfig;
//
//    public EncryptUtils(BaiWeiConfig baiWeiConfig) {
//        this.baiWeiConfig = baiWeiConfig;
//    }


    private FqlConfig baiWeiConfig;

    public EncryptUtils(FqlConfig baiWeiConfig) {
        this.baiWeiConfig = baiWeiConfig;
    }

    private static final Logger logger = LoggerFactory.getLogger(EncryptUtils.class);

    /**
     * 加密加签
     */
    public  FqlCommonRequest encryptUtils(Object object) {
        LxClient lxClient = new LxClient();
        if (object == null) {
            throw new IllegalArgumentException("待加密对象不能为null");
        }
        FqlCommonRequest fqlCommonRequest = new FqlCommonRequest();
        String request = JSON.toJSONString(object);
        logger.info("{} 出参原始报文: {}, {}", request);

            //没有业务返回数据就不需要加签、加密
            if (request.length() > 0) {
                //响应data加密、加签
                //先对请求数据进行加密 需改动 TODO
                //String encrptString = lxClient.encrypt(response.getBizContent(), config.getLxPublicKey());
                //String encrptString = lxClient.encrypt(request,  baiWeiConfig.getBwPublicKey());
                String encrptString = lxClient.encrypt(request,"MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCN6w4G1gZnUdEAH1bNO6JpnwsyTCWpkwOMaOLDXYGM1TcnX1SF1FrdjVlRyf1iPiSZgbFy0+bjkwwZ9ko8+ygYaSiREuNV7wN6eY2SdOpfYm1CB3UM56Q7Wh2gwgiq28CHE1f24qsGnq8fe2gUti1WNXDiDpMf8YsMQkda1qVSMwIDAQAB");
                fqlCommonRequest.setBizContent(encrptString);
                fqlCommonRequest.setTimestamp(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                fqlCommonRequest.setPartnerCode("FQLQY001");

                //对整个请求对象进行加签 需改动 TODO
                String data = JSON.toJSONString(fqlCommonRequest, SerializerFeature.MapSortField);

                //String sign = lxClient.sign(data, config.getPartnerSignPrivateKey());
                //String sign = lxClient.sign(data, baiWeiConfig.getPartnerSignPrivateKey2());
                String sign = lxClient.sign(data, "MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBALHgle959drURIEBvTcxcT1r7Y53heG7yK8ry8aOuY/fc4HrJ9Q/eEsYwciy16vIsFdRjIMlA2K7ETzA0MhU5LVLw6SXeqdLrekirLWtsxdWjlgLvwuKrk+t34xaCDGTnngEZUtAtpd4xZ+fpUKZmZLj4yyAVUB73EGFYiTQRdizAgMBAAECgYAEaEapcjGpHuT691KQuV0PbbWrTS3YNoFrSbVZBvSfv88fsyEVArpAeMK7GNH8M9L0Duq23dC+064X0xqOjtVDLW7WCTi5bxUrkmwxozU8pEu0/7eq9UM/P/fx5onwFjEW0KqGcR9aUdVPKh+BRrFwiZA9r3pbxx9TVYAzp5y/ZQJBAOkeRYtH94WyWoDg73TUGlPFINwQ2YykGRMJ43Sgdnw7FwQAYo7+NhIacACaJUmxUijuRSJO6Re3K1NYJEbKTy0CQQDDVjwr2E0qmbx3vWskKN8u7Q+nY/4VVwpMB9cDBEJWBupGoHv4WrEkuUUZTUPn0rks+UdecDC+R290JFui7rNfAkEAoqvBE6QwkVcX2H8eGYQ4quQQPgB0DrQj2yk3U5b1l1MUiHJMVEQILzHLnl/yTS4ziuRZ0csG1Mm4rfv/tHZQMQJAXGKoysPem0tiy+8WgV+jTvpn8O9l+InWIOeEVbTp+u4CV60HdQrPxWKqv7C/cSFE23R6wLunEhePKwsXHBRxWQJBALFC2Y9uCp0r2apb+Yne+dw5cBm5uXkpMcNO20C0Y+gEXCEMUcPvrfJo3oQHmzg2GfHT/P1GDgTqycwO8SEeZxw=");
                //对请求数据进行加签
                fqlCommonRequest.setSign(sign);
            }
            return  fqlCommonRequest;
    }

    /**
     * 验签解密
     */
    public String decryptUtils(Object object) {
        LxClient lxClient = new LxClient();
        String decrptString = new String();
        //分期乐请求合作方
        //反解析成对象
        String s = JSON.toJSONString(object);
        logger.error("{} 解析内容,{}", s);
        com.alibaba.fastjson.JSONObject request = JSON.parseObject(s);
        String sign = request.getString("sign");
        request.remove("sign");
        String data = JSON.toJSONString(request, SerializerFeature.MapSortField);

        //验签需改动 TODO
        //boolean checkSign = lxClient.verifySign(data, sign, baiWeiConfig.getBwSignPublicKey());
        boolean checkSign = lxClient.verifySign(data, sign, "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCN6w4G1gZnUdEAH1bNO6JpnwsyTCWpkwOMaOLDXYGM1TcnX1SF1FrdjVlRyf1iPiSZgbFy0+bjkwwZ9ko8+ygYaSiREuNV7wN6eY2SdOpfYm1CB3UM56Q7Wh2gwgiq28CHE1f24qsGnq8fe2gUti1WNXDiDpMf8YsMQkda1qVSMwIDAQAB");
        if (!checkSign) {
            logger.error("{} 验签失败,{}", request);
            throw new FqlBizException(FqlResultCode.SIGN_VERIFY_FAIL);
        }
        try {
            //验签需改动 TODO
            //decrptString = lxClient.decrypt(request.getString("bizContent"), baiWeiConfig.getPartnerPrivateKey2());
            decrptString = lxClient.decrypt(request.getString("bizContent"), "MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBALHgle959drURIEBvTcxcT1r7Y53heG7yK8ry8aOuY/fc4HrJ9Q/eEsYwciy16vIsFdRjIMlA2K7ETzA0MhU5LVLw6SXeqdLrekirLWtsxdWjlgLvwuKrk+t34xaCDGTnngEZUtAtpd4xZ+fpUKZmZLj4yyAVUB73EGFYiTQRdizAgMBAAECgYAEaEapcjGpHuT691KQuV0PbbWrTS3YNoFrSbVZBvSfv88fsyEVArpAeMK7GNH8M9L0Duq23dC+064X0xqOjtVDLW7WCTi5bxUrkmwxozU8pEu0/7eq9UM/P/fx5onwFjEW0KqGcR9aUdVPKh+BRrFwiZA9r3pbxx9TVYAzp5y/ZQJBAOkeRYtH94WyWoDg73TUGlPFINwQ2YykGRMJ43Sgdnw7FwQAYo7+NhIacACaJUmxUijuRSJO6Re3K1NYJEbKTy0CQQDDVjwr2E0qmbx3vWskKN8u7Q+nY/4VVwpMB9cDBEJWBupGoHv4WrEkuUUZTUPn0rks+UdecDC+R290JFui7rNfAkEAoqvBE6QwkVcX2H8eGYQ4quQQPgB0DrQj2yk3U5b1l1MUiHJMVEQILzHLnl/yTS4ziuRZ0csG1Mm4rfv/tHZQMQJAXGKoysPem0tiy+8WgV+jTvpn8O9l+InWIOeEVbTp+u4CV60HdQrPxWKqv7C/cSFE23R6wLunEhePKwsXHBRxWQJBALFC2Y9uCp0r2apb+Yne+dw5cBm5uXkpMcNO20C0Y+gEXCEMUcPvrfJo3oQHmzg2GfHT/P1GDgTqycwO8SEeZxw=");
            logger.info("{} 入参解密后报文: {},{}", decrptString);
        } catch (Exception e) {
            logger.error("{} 解密失败,{}", e);
            throw new FqlBizException(FqlResultCode.SIGN_VERIFY_FAIL);
        }
        return decrptString;
    }

}
