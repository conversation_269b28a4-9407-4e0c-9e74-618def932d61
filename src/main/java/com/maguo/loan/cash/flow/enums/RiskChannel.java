package com.maguo.loan.cash.flow.enums;

/**
 * 外部风控渠道
 */
public enum RiskChannel {

    BW("百维");

    private final String desc;

    RiskChannel(String desc) {
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public static RiskChannel getFlowChannel(String name) {
        for (RiskChannel flowChannel : RiskChannel.values()) {
            if (flowChannel.name().equals(name)) {
                return flowChannel;
            }
        }
        return null;
    }

}
