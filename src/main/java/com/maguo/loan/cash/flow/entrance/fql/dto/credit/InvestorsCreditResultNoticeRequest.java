package com.maguo.loan.cash.flow.entrance.fql.dto.credit;

import jakarta.validation.constraints.NotBlank;

/*
 * @Description: 授信申请查询请求
 * @Author: abai
 * @Date: 2025-8-11 下午 02:10
 */
public class InvestorsCreditResultNoticeRequest {

    /**
     * 授信申请编号
     */
    @NotBlank(message = "授信申请编号不能为空")
    private String applyId;

    /**
     * 合作方代码
     */
    @NotBlank(message = "合作方代码不能为空")
    private String partnerCode;


    public String getApplyId() {
        return applyId;
    }

    public void setApplyId(String applyId) {
        this.applyId = applyId;
    }

    public String getPartnerCode() {
        return partnerCode;
    }

    public void setPartnerCode(String partnerCode) {
        this.partnerCode = partnerCode;
    }

}
