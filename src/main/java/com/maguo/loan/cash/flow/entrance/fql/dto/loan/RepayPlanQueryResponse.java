package com.maguo.loan.cash.flow.entrance.fql.dto.loan;

import java.math.BigDecimal;
import java.util.List;

/*
 * @Description: 分期乐还款计划查询响应
 * @Author: abai
 * @Date: 2025-8-11 下午 05:29
 */
public class RepayPlanQueryResponse {
    /**
     * 响应状态，0-成功，1-失败
     */
    private Integer status;

    /**
     * 错误描述
     */
    private String msg;

    /**
     * 扩展字段
     */
    private String extendFields;

    /**
     * 借款总期数
     */
    private Integer totalTerm;

    /**
     * 借据总金额(元)
     */
    private BigDecimal totalAmount;

    /**
     * 还款计划列表
     */
    private List<FenQiLeRepayPlan> repayPlans;

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public String getExtendFields() {
        return extendFields;
    }

    public void setExtendFields(String extendFields) {
        this.extendFields = extendFields;
    }

    public Integer getTotalTerm() {
        return totalTerm;
    }

    public void setTotalTerm(Integer totalTerm) {
        this.totalTerm = totalTerm;
    }

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    public List<FenQiLeRepayPlan> getRepayPlans() {
        return repayPlans;
    }

    public void setRepayPlans(List<FenQiLeRepayPlan> repayPlans) {
        this.repayPlans = repayPlans;
    }
}
