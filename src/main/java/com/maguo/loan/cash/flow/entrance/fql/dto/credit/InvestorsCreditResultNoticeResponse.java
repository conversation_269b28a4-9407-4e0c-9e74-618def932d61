package com.maguo.loan.cash.flow.entrance.fql.dto.credit;

/*
 * @Description: 授信申请查询响应
 * @Author: abai
 * @Date: 2025-8-11 下午 02:09
 */
public class InvestorsCreditResultNoticeResponse {

    /**
     * 授信审批状态,0-授信成功，1-授信失败，2-授信审批中，3-查无此单
     */
    private Integer status;

    /**
     * 授信审批描述
     */
    private String msg;

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public static InvestorsCreditResultNoticeResponse success() {
        InvestorsCreditResultNoticeResponse result = new InvestorsCreditResultNoticeResponse();
        result.setStatus(3);
        result.setMsg("请求成功");
        return result;
    }
    public static InvestorsCreditResultNoticeResponse fail() {
        InvestorsCreditResultNoticeResponse result = new InvestorsCreditResultNoticeResponse();
        result.setStatus(1);
        result.setMsg("请求失败");
        return result;
    }
    public static InvestorsCreditResultNoticeResponse fail(String msg) {
        InvestorsCreditResultNoticeResponse result = new InvestorsCreditResultNoticeResponse();
        result.setStatus(1);
        result.setMsg(msg);
        return result;
    }
    public static InvestorsCreditResultNoticeResponse processing() {
        InvestorsCreditResultNoticeResponse result = new InvestorsCreditResultNoticeResponse();
        result.setStatus(2);
        result.setMsg("授信处理中");
        return result;
    }
    public static InvestorsCreditResultNoticeResponse unknowOrder(String msg) {
        InvestorsCreditResultNoticeResponse result = new InvestorsCreditResultNoticeResponse();
        result.setStatus(3);
        result.setMsg(msg);
        return result;
    }
}
