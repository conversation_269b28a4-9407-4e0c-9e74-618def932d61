package com.maguo.loan.cash.flow.entrance.fql.dto.repay.response;

import com.maguo.loan.cash.flow.entrance.fql.dto.repay.request.OtherInfo;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Description 还款账单明细 响应参数
 * @Date 2025/08/08 16:51
 * @Version v1.0
 **/
public class RpyDetailsResponse {

    /**
     * 账单维度的还款总额（单位：元）
     */
    private BigDecimal rpyAmt;
    /**
     * 实还本金（单位：元）
     */
    private BigDecimal rpyPrincipal;
    /**
     * 实还利息（单位：元）
     */
    private BigDecimal rpyFeeAmt;
    /**
     * 实还罚息（单位：元）
     */
    private BigDecimal rpyMuclt;
    /**
     * 可能会其他类型的金额,用json数组的格式提高扩展性
     * 其他科目金额
     */
    private List<OtherInfo> otherInfo;
    /**
     * 还款期数
     */
    private Integer rpyTerm;
    /**
     * 代扣日期
     */
    private String rpyDate;

    public BigDecimal getRpyAmt() {
        return rpyAmt;
    }

    public void setRpyAmt(BigDecimal rpyAmt) {
        this.rpyAmt = rpyAmt;
    }

    public BigDecimal getRpyPrincipal() {
        return rpyPrincipal;
    }

    public void setRpyPrincipal(BigDecimal rpyPrincipal) {
        this.rpyPrincipal = rpyPrincipal;
    }

    public BigDecimal getRpyFeeAmt() {
        return rpyFeeAmt;
    }

    public void setRpyFeeAmt(BigDecimal rpyFeeAmt) {
        this.rpyFeeAmt = rpyFeeAmt;
    }

    public BigDecimal getRpyMuclt() {
        return rpyMuclt;
    }

    public void setRpyMuclt(BigDecimal rpyMuclt) {
        this.rpyMuclt = rpyMuclt;
    }

    public List<OtherInfo> getOtherInfo() {
        return otherInfo;
    }

    public void setOtherInfo(List<OtherInfo> otherInfo) {
        this.otherInfo = otherInfo;
    }

    public Integer getRpyTerm() {
        return rpyTerm;
    }

    public void setRpyTerm(Integer rpyTerm) {
        this.rpyTerm = rpyTerm;
    }

    public String getRpyDate() {
        return rpyDate;
    }

    public void setRpyDate(String rpyDate) {
        this.rpyDate = rpyDate;
    }
}
