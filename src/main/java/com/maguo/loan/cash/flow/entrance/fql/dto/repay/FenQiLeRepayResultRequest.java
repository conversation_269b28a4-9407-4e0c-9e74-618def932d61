package com.maguo.loan.cash.flow.entrance.fql.dto.repay;

import jakarta.validation.constraints.NotBlank;

/**
 * @Description 代扣结果查询 请求参数
 * @Date 2025/08/08 16:51
 * @Version v1.0
 **/
public class FenQiLeRepayResultRequest {

    /**
     * 代扣请求流水号
     */
    @NotBlank(message = "代扣请求流水号不能为空")
    private String withholdSerialNo;

    /**
     * 合作方代码
     */
    @NotBlank(message = "合作方代码不能为空")
    private String partnerCode;

    public String getWithholdSerialNo() {
        return withholdSerialNo;
    }

    public void setWithholdSerialNo(String withholdSerialNo) {
        this.withholdSerialNo = withholdSerialNo;
    }

    public String getPartnerCode() {
        return partnerCode;
    }

    public void setPartnerCode(String partnerCode) {
        this.partnerCode = partnerCode;
    }
}
