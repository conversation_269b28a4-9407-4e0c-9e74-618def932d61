package com.maguo.loan.cash.flow.entrance.fql.filter.sdk.utils;

import com.maguo.loan.cash.flow.entrance.fql.filter.sdk.LxException;
import com.maguo.loan.cash.flow.entrance.fql.filter.sdk.enums.KeyFactoryEnum;
import com.maguo.loan.cash.flow.entrance.fql.filter.sdk.enums.SignatureAlgorithmEnum;

import javax.xml.bind.DatatypeConverter;
import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.security.KeyFactory;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.Signature;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;

/**
 * <AUTHOR>
 * @Description  RSA加签   解签的实现方式
 * @Date 2020/4/23 14:45
 */

public class RsaSignManager {
    //做成单例
    public static  PrivateKey privateKey = null;
    public static  PublicKey publicKey = null;


    //TODO 需要模板替换
    private SignatureAlgorithmEnum signatureAlgorithmEnum = SignatureAlgorithmEnum.SHA256WITHRSA;

    private KeyFactoryEnum keyFactoryEnum = KeyFactoryEnum.RSA;


    /**
     *  加密接口
     */
    public  String sign(String signData, String keyPath){
        try {
            Signature signature = Signature.getInstance(signatureAlgorithmEnum.getKey());
            signature.initSign(getPriKey(keyPath));
            signature.update(signData.getBytes());
            return byteToBase64(signature.sign());
        } catch (Exception e){
           // log.error("",e);
            throw new LxException(e.getMessage());
        }
    }

    /**
     *  解密接口
     */
    public  boolean verifySign(String signData, String keyPath,String returnSignData){
        try {
            PublicKey publicK = getPubKey(keyPath);
            Signature signature = Signature.getInstance(signatureAlgorithmEnum.getKey());
            signature.initVerify(publicK);
            signature.update(signData.getBytes());
            return signature.verify(base64ToByte(returnSignData));
        } catch (Exception e){
            //log.error("",e);
            throw new LxException(e.getMessage());
        }
    }


    /**
     * java 使用的是pkcs8格式的私钥，一般都是pkcs1的，所以需要将其转换为pkcs8格式的
     * 该函数接受的是pkcs1格式的私钥文件（PEM）格式
     * @param priPath
     * @return
     * @throws Exception
     *
     */
    private  PrivateKey getPriKey(String priPath) throws LxException {
        if(privateKey != null){
            return privateKey;
        }
//        if(StringUtils.isBlank(priPath)){
//            throw new TestException("priPath为空" );
//        }
        File file = new File(priPath);
        String certData = priPath;
        if(file.isFile()){
            certData = getCertDataByFilePath(priPath);
        }

        try{
            byte[] keyBytes = base64ToByte(certData);
            PKCS8EncodedKeySpec pkcs8KeySpec = new PKCS8EncodedKeySpec(keyBytes);
            KeyFactory keyFactory = KeyFactory.getInstance(keyFactoryEnum.getKey());
            privateKey =  keyFactory.generatePrivate(pkcs8KeySpec);
            return privateKey;
        }catch (Exception e){
           // log.error("getPrikey失败异常",e);
            throw new LxException("getPrikey失败" );
        }

    }

    //根据文件路径读取证书数据
    private String getCertDataByFilePath(String filePath){
        try(BufferedReader br = new BufferedReader(new FileReader(filePath));){
            String s = br.readLine();
            StringBuilder str = new StringBuilder();
            s = br.readLine();
            while (s.charAt(0) != '-'){
                str.append(s + "\r");
                s = br.readLine();
            }
            return s;
        }catch (Exception e){
            //log.info("失败异常",e);
            throw new LxException("根据文件路径读取证书数据失败" );
        }
    }



    private  String byteToBase64(byte[] data)  {
        return DatatypeConverter.printBase64Binary(data);
    }

    private  byte[] base64ToByte(String data) {
        return DatatypeConverter.parseBase64Binary(data);
    }

    /**
     * 通过字符串生成公钥
     */
    public  PublicKey getPubKey(String pubPath) throws LxException {
        if(publicKey != null){
            return publicKey;
        }
//        if(StringUtils.isBlank(pubPath)){
//            throw new TestException("pubPath" );
//        }
        File file = new File(pubPath);
        String certData = pubPath;
        if(file.isFile()){
            certData = getCertDataByFilePath(pubPath);
        }
        try{
            byte[] decodeKey = base64ToByte(certData);
            X509EncodedKeySpec x509 = new X509EncodedKeySpec(decodeKey);
            KeyFactory keyFactory = KeyFactory.getInstance(keyFactoryEnum.getKey());
            publicKey = keyFactory.generatePublic(x509);
            return publicKey;
        }catch (Exception e){
         //   log.error("getPubkey失败异常",e);
            throw new LxException("getPubkey失败异常" );
        }
    }

}
