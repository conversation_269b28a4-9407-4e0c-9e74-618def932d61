package com.maguo.loan.cash.flow.entity;

import com.maguo.loan.cash.flow.enums.EquityRecipient;
import com.maguo.loan.cash.flow.enums.IsIncludingEquity;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotBlank;

import java.math.BigDecimal;

/*
 * @Description: 分期乐放款申请
 */
@Entity
@Table(name = "fql_loan_apply_record")
public class FqlLoanApplyRecord extends BaseEntity{
    /**
     * 用信审批申请编号
     */
    private String applyId;
    /**
     * 合作方代码
     */
    private String partnerCode;
    /**
     * 资产类型
     */
    private Integer orderType;
    /**
     * 借款金额
     */
    private BigDecimal loanPrincipal;

    /**
     * 借款期数
     */
    private Integer loanTerm;

    /**
     * 年化利率
     */
    private BigDecimal annualRate;

    /**
     * 申请贷款用途
     */
    private String loanUse;

    /**
     * 手机号
     */
    private String mobileNo;
    /**
     * 借款人收款户名
     */
    private String debitAccountName;

    /**
     * 收款人银行卡开户行
     */
    private String debitOpenAccountBank;

    /**
     * 收款人银行卡卡号
     */
    private String debitAccountNo;

    /**
     * 开户行编码
     */
    private String paymentBankCode;

    /**
     * 收款卡联行号
     */
    private String debitCnaps;

    /**
     * 还款方式
     */
    private Integer repayType;

    /**
     * 扩展信息
     */
    private String extend;

    /**
     * 是否含权益
     * Y:含权益：N:不含权益
     */
    @Enumerated(EnumType.STRING)
    private IsIncludingEquity isIncludingEquity;

    /**
     * 权益收取方
     * O：外部,I：内部（默认O：外部）
     */
    @Enumerated(EnumType.STRING)
    private EquityRecipient equityRecipient;

    public String getApplyId() {
        return applyId;
    }

    public void setApplyId(String applyId) {
        this.applyId = applyId;
    }

    public String getPartnerCode() {
        return partnerCode;
    }

    public void setPartnerCode(String partnerCode) {
        this.partnerCode = partnerCode;
    }

    public Integer getOrderType() {
        return orderType;
    }

    public void setOrderType(Integer orderType) {
        this.orderType = orderType;
    }

    public BigDecimal getLoanPrincipal() {
        return loanPrincipal;
    }

    public void setLoanPrincipal(BigDecimal loanPrincipal) {
        this.loanPrincipal = loanPrincipal;
    }

    public Integer getLoanTerm() {
        return loanTerm;
    }

    public void setLoanTerm(Integer loanTerm) {
        this.loanTerm = loanTerm;
    }

    public BigDecimal getAnnualRate() {
        return annualRate;
    }

    public void setAnnualRate(BigDecimal annualRate) {
        this.annualRate = annualRate;
    }

    public String getLoanUse() {
        return loanUse;
    }

    public void setLoanUse(String loanUse) {
        this.loanUse = loanUse;
    }

    public String getMobileNo() {
        return mobileNo;
    }

    public void setMobileNo(String mobileNo) {
        this.mobileNo = mobileNo;
    }

    public String getDebitAccountName() {
        return debitAccountName;
    }

    public void setDebitAccountName(String debitAccountName) {
        this.debitAccountName = debitAccountName;
    }

    public String getDebitOpenAccountBank() {
        return debitOpenAccountBank;
    }

    public void setDebitOpenAccountBank(String debitOpenAccountBank) {
        this.debitOpenAccountBank = debitOpenAccountBank;
    }

    public String getDebitAccountNo() {
        return debitAccountNo;
    }

    public void setDebitAccountNo(String debitAccountNo) {
        this.debitAccountNo = debitAccountNo;
    }

    public String getPaymentBankCode() {
        return paymentBankCode;
    }

    public void setPaymentBankCode(String paymentBankCode) {
        this.paymentBankCode = paymentBankCode;
    }

    public String getDebitCnaps() {
        return debitCnaps;
    }

    public void setDebitCnaps(String debitCnaps) {
        this.debitCnaps = debitCnaps;
    }

    public Integer getRepayType() {
        return repayType;
    }

    public void setRepayType(Integer repayType) {
        this.repayType = repayType;
    }

    public String getExtend() {
        return extend;
    }

    public void setExtend(String extend) {
        this.extend = extend;
    }

    public IsIncludingEquity getIsIncludingEquity() {
        return isIncludingEquity;
    }

    public void setIsIncludingEquity(IsIncludingEquity isIncludingEquity) {
        this.isIncludingEquity = isIncludingEquity;
    }

    public EquityRecipient getEquityRecipient() {
        return equityRecipient;
    }

    public void setEquityRecipient(EquityRecipient equityRecipient) {
        this.equityRecipient = equityRecipient;
    }
}
