package com.maguo.loan.cash.flow.entrance.fql.dto.repay;

import jakarta.validation.constraints.NotBlank;

/**
 * @ClassName RealReturnResultRequest
 * <AUTHOR>
 * @Description 实还通知查询接口请求参数
 * @Date 2025/8/11 14:37
 * @Version v1.0
 **/
public class FenQiLeRealReturnResultRequest {

    /**
     * 合作方代码（资方定义，给乐信分配的代码）
     */
    @NotBlank(message = "合作方代码不能为空")
    private String partnerCode;
    /**
     * 还款请求流水号/账单号
     */
    @NotBlank(message = "还款请求流水号不能为空")
    private String billId;
    /**
     * 资金方放款编号/借据号
     */
    @NotBlank(message = "资金方放款编号不能为空")
    private String capitalLoanNo;

    public String getPartnerCode() {
        return partnerCode;
    }

    public void setPartnerCode(String partnerCode) {
        this.partnerCode = partnerCode;
    }

    public String getBillId() {
        return billId;
    }

    public void setBillId(String billId) {
        this.billId = billId;
    }

    public String getCapitalLoanNo() {
        return capitalLoanNo;
    }

    public void setCapitalLoanNo(String capitalLoanNo) {
        this.capitalLoanNo = capitalLoanNo;
    }
}
