package com.maguo.loan.cash.flow.entrance.fql.enums;

/**
 * <AUTHOR>
 * @Description 还款申请返回状态码
 * @Date 2025/8/6 15:20
 * @Version v1.0
 **/
public enum FenQiLeRepayReturnStatusEnum {

    SUCCESS(1, "接口通知成功"),
    FAIL(2, "接口通知失败");

    private Integer code;

    private String desc;

    FenQiLeRepayReturnStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

}
