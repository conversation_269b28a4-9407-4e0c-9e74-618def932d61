package com.maguo.loan.cash.flow.entrance.fql.dto.credit;

import jakarta.validation.constraints.NotBlank;

/*
 * @Description: 分期乐影像信息
 * @Author: abai
 * @Date: 2025-8-8 下午 01:51
 */
public class UpLoadInfo {
    /**
     * 文件类型
     */
    @NotBlank(message = "文件类型不能为空")
    private String fileType;
    /**
     * 联系人姓名
     */
    @NotBlank(message = "文件路径不能为空")
    private String filePath;
    /**
     * 联系人姓名
     */
    @NotBlank(message = "文件名不能为空")
    private String fileName;

    public String getFileType() {
        return fileType;
    }

    public void setFileType(String fileType) {
        this.fileType = fileType;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }
}
