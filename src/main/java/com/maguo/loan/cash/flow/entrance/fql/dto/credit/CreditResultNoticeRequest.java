package com.maguo.loan.cash.flow.entrance.fql.dto.credit;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/*
 * @Description: 授信申请查询请求
 * @Author: abai
 * @Date: 2025-8-11 下午 02:10
 */
public class CreditResultNoticeRequest {

    /**
     * 授信申请编号
     */
    @NotBlank(message = "授信申请编号不能为空")
    private String applyId;

    /**
     * 合作方代码
     */
    @NotBlank(message = "合作方代码不能为空")
    private String partnerCode;

    /**
     * 授信审批状态,0-授信成功，1-授信失败，2-授信审批中，3-查无此单
     */
    @NotNull(message = "审批授信状态不能为空")
    private Integer status;

    /**
     * 授信审批描述
     */
    private String msg;


    public String getApplyId() {
        return applyId;
    }

    public void setApplyId(String applyId) {
        this.applyId = applyId;
    }

    public String getPartnerCode() {
        return partnerCode;
    }

    public void setPartnerCode(String partnerCode) {
        this.partnerCode = partnerCode;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }
}
