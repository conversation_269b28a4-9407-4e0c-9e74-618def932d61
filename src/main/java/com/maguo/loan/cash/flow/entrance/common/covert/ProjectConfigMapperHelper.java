package com.maguo.loan.cash.flow.entrance.common.covert;

import com.ctrip.framework.apollo.core.utils.StringUtils;
import com.google.inject.name.Named;
import com.jinghang.capital.api.dto.BankChannel;
import com.jinghang.cash.api.dto.ProjectInfoDto;
import com.maguo.loan.cash.flow.entrance.common.service.ProjectInfoService;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import com.maguo.loan.cash.flow.job.ppd.PPDClearDownloadJob;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class ProjectConfigMapperHelper {

    private static final Logger logger = LoggerFactory.getLogger(PPDClearDownloadJob.class);
    @Autowired
    private ProjectInfoService projectInfoService;


    @Named("getBankChannelFromProject")
    public BankChannel getBankChannelFromProject(String projectCode) {

        try {
            ProjectInfoDto projectElements = projectInfoService.queryProjectInfo(projectCode);
            if (projectElements != null && projectElements.getCapitalChannel() != null) {
                String channelCode = projectElements.getCapitalChannel();

                return BankChannel.valueOf(channelCode);
            }
        } catch (Exception e) {
            logger.error("根据 projectCode [{}] 获取项目银行渠道失败", projectCode, e);
        }

        return null;
    }

    @Named("getFlowChannelFromProject")
    public FlowChannel getFlowChannelFromProject(String projectCode) {
        try {
            ProjectInfoDto projectElements = projectInfoService.queryProjectInfo(projectCode);
            if (projectElements != null && !StringUtils.isBlank(projectElements.getFlowChannel())) {
                String flowChannel = projectElements.getFlowChannel();
                return FlowChannel.valueOf(flowChannel);
            }
        } catch (Exception e) {
            logger.error("根据 projectCode [{}] 获取项目流量渠道失败", projectCode, e);
        }
        return null;
    }
}
