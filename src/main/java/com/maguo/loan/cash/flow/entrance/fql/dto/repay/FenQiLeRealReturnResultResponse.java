package com.maguo.loan.cash.flow.entrance.fql.dto.repay;

import java.math.BigDecimal;

/**
 * @ClassName RealReturnResultResponse
 * <AUTHOR>
 * @Description 实还通知查询接口响应参数
 * @Date 2025/8/11 14:37
 * @Version v1.0
 **/
public class FenQiLeRealReturnResultResponse {

    /**
     * 还款状态
     * 1、成功(结算成功)
     * 2、失败(结算失败)
     * 3. 还款中(结算处理中)
     * 4. 查无此通知（乐信侧会重复发起还款）
     */
    private Integer repayStatus;
    /**
     * 处理成功时间
     */
    private String processTime;
    /**
     * 还款描述
     */
    private String msg;
    /**
     * 还款总额,保留两位有效数字(单位:元)
     */
    private BigDecimal repayAmount;
    /**
     * 实还本金,保留两位有效数字(单位:元)
     */
    private BigDecimal repayPrincipal;
    /**
     * 实还利息,保留两位有效数字(单位:元)
     */
    private BigDecimal repayInterest;
    /**
     * 实还罚息,保留两位有效数字(单位:元)
     */
    private BigDecimal repayMuclt;
    /**
     * 实还担保费,保留两位有效数字(单位:元)
     */
    private BigDecimal repayGuarantee;
    /**
     * 实还信用评估费,保留两位有效数字(单位:元)
     */
    private BigDecimal repayCreditFee;
    /**
     * 实还咨询服务费,保留两位有效数字(单位:元)
     */
    private BigDecimal repayGranteeConsultServiceFee;

    public Integer getRepayStatus() {
        return repayStatus;
    }

    public void setRepayStatus(Integer repayStatus) {
        this.repayStatus = repayStatus;
    }

    public String getProcessTime() {
        return processTime;
    }

    public void setProcessTime(String processTime) {
        this.processTime = processTime;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public BigDecimal getRepayAmount() {
        return repayAmount;
    }

    public void setRepayAmount(BigDecimal repayAmount) {
        this.repayAmount = repayAmount;
    }

    public BigDecimal getRepayPrincipal() {
        return repayPrincipal;
    }

    public void setRepayPrincipal(BigDecimal repayPrincipal) {
        this.repayPrincipal = repayPrincipal;
    }

    public BigDecimal getRepayInterest() {
        return repayInterest;
    }

    public void setRepayInterest(BigDecimal repayInterest) {
        this.repayInterest = repayInterest;
    }

    public BigDecimal getRepayMuclt() {
        return repayMuclt;
    }

    public void setRepayMuclt(BigDecimal repayMuclt) {
        this.repayMuclt = repayMuclt;
    }

    public BigDecimal getRepayGuarantee() {
        return repayGuarantee;
    }

    public void setRepayGuarantee(BigDecimal repayGuarantee) {
        this.repayGuarantee = repayGuarantee;
    }

    public BigDecimal getRepayCreditFee() {
        return repayCreditFee;
    }

    public void setRepayCreditFee(BigDecimal repayCreditFee) {
        this.repayCreditFee = repayCreditFee;
    }

    public BigDecimal getRepayGranteeConsultServiceFee() {
        return repayGranteeConsultServiceFee;
    }

    public void setRepayGranteeConsultServiceFee(BigDecimal repayGranteeConsultServiceFee) {
        this.repayGranteeConsultServiceFee = repayGranteeConsultServiceFee;
    }

    public static FenQiLeRealReturnResultResponse fail(String msg) {
        FenQiLeRealReturnResultResponse result = new FenQiLeRealReturnResultResponse();
        result.setRepayStatus(2);
        result.setMsg(msg);
        return result;
    }
}
