package com.maguo.loan.cash.flow.entity;

import com.maguo.loan.cash.flow.enums.AmountType;
import com.maguo.loan.cash.flow.enums.RiskChannel;
import com.maguo.loan.cash.flow.enums.ApplyType;
import com.maguo.loan.cash.flow.enums.AuditState;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import com.maguo.loan.cash.flow.enums.MarketingChannel;
import com.maguo.loan.cash.flow.enums.RateLevel;
import com.maguo.loan.cash.flow.enums.RightsLevel;
import com.maguo.loan.cash.flow.enums.RightsPayType;
import com.maguo.loan.cash.flow.enums.WhetherState;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 外部风控
 * <AUTHOR>
 * @date 2025/8/18
 */
@Entity
@Table(name = "user_risk_record_external")
public class UserRiskRecordExternal extends BaseEntity {
    private String userId;

    /**
     * 渠道标签
     */
    private String applyChannel;

    @Enumerated(EnumType.STRING)
    private FlowChannel flowChannel;

    @Enumerated(EnumType.STRING)
    private RiskChannel riskChannel;

    @Enumerated(EnumType.STRING)
    private AmountType amountType;

    @Enumerated(EnumType.STRING)
    private ApplyType applyType;

    @Enumerated(EnumType.STRING)
    private RightsPayType rightsPayType;

    /**
     * 规则代码
     */
    private String ruleCode;
    /**
     * 规则描述
     */
    private String ruleDesc;
    /**
     * 审批结果代码 AA AD AP
     */
    private String approveResultCode;
    /**
     * 审批结果 AA  AD AP
     */
    @Enumerated(EnumType.STRING)
    private AuditState approveResult;
    /**
     * 建议额度
     */
    private BigDecimal approveAmount;
    /**
     * 风控审批权益
     */
    @Enumerated(EnumType.STRING)
    private RightsLevel approveRights;

    /**
     * 风控审批利率
     */
    @Enumerated(EnumType.STRING)
    private RateLevel approveRate;

    /**
     * 风控最终执行返回结果
     */
    private String riskFinalResult;
    /**
     * 通过时间
     */
    private LocalDateTime passTime;
    /**
     * 有效期
     */
    private LocalDate expireTime;
    /**
     * 是否强制购买权益
     */
    @Enumerated(EnumType.STRING)
    private WhetherState approveRightsForce;

    /**
     * 客户风险评分
     */
    private String approveScore;
    /**
     * 风控返回的用户风险等级
     */
    private Integer riskLevel;

    public Integer getRiskLevel() {
        return riskLevel;
    }

    public void setRiskLevel(Integer riskLevel) {
        this.riskLevel = riskLevel;
    }

    /**
     * 是否调用中原风控
     */
    @Enumerated(EnumType.STRING)
    private WhetherState callZyRisk;
    /**
     * 子渠道标识
     */
    private String subChannel;

    /**
     * 营销渠道
     */
    @Enumerated(EnumType.STRING)
    private MarketingChannel marketingChannel;
    /**
     * 风控策略
     */
    private String policyName;
    /**
     * A卡评分(欺诈风险)
     */
    private String aCardScore;

    /**
     * 用信意愿度
     */
    private String willingness;

    /**
     * 客户分层标签
     */
    private String customerLabel;

    /**
     * 外部风控平台id
     */
    private String pipelineId;

    /**
     * 项目唯一编码
     *  add by yunhengtong 20250821 start
     */
    private String projectCode;

    public String getaCardScore() {
        return aCardScore;
    }

    public void setaCardScore(String aCardScore) {
        this.aCardScore = aCardScore;
    }

    public String getWillingness() {
        return willingness;
    }

    public void setWillingness(String willingness) {
        this.willingness = willingness;
    }

    public String getCustomerLabel() {
        return customerLabel;
    }

    public void setCustomerLabel(String customerLabel) {
        this.customerLabel = customerLabel;
    }

    public String getSubChannel() {
        return subChannel;
    }

    public void setSubChannel(String subChannel) {
        this.subChannel = subChannel;
    }

    public WhetherState getApproveRightsForce() {
        return approveRightsForce;
    }

    public void setApproveRightsForce(WhetherState approveRightsForce) {
        this.approveRightsForce = approveRightsForce;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public FlowChannel getFlowChannel() {
        return flowChannel;
    }

    public void setFlowChannel(FlowChannel flowChannel) {
        this.flowChannel = flowChannel;
    }

    public AmountType getAmountType() {
        return amountType;
    }

    public void setAmountType(AmountType amountType) {
        this.amountType = amountType;
    }

    public ApplyType getApplyType() {
        return applyType;
    }

    public void setApplyType(ApplyType applyType) {
        this.applyType = applyType;
    }

    public String getRuleCode() {
        return ruleCode;
    }

    public void setRuleCode(String ruleCode) {
        this.ruleCode = ruleCode;
    }

    public String getRuleDesc() {
        return ruleDesc;
    }

    public void setRuleDesc(String ruleDesc) {
        this.ruleDesc = ruleDesc;
    }

    public String getApproveResultCode() {
        return approveResultCode;
    }

    public void setApproveResultCode(String approveResultCode) {
        this.approveResultCode = approveResultCode;
    }

    public AuditState getApproveResult() {
        return approveResult;
    }

    public void setApproveResult(AuditState approveResult) {
        this.approveResult = approveResult;
    }

    public BigDecimal getApproveAmount() {
        return approveAmount;
    }

    public void setApproveAmount(BigDecimal approveAmount) {
        this.approveAmount = approveAmount;
    }

    public RightsLevel getApproveRights() {
        return approveRights;
    }

    public void setApproveRights(RightsLevel approveRights) {
        this.approveRights = approveRights;
    }

    public String getRiskFinalResult() {
        return riskFinalResult;
    }

    public void setRiskFinalResult(String riskFinalResult) {
        this.riskFinalResult = riskFinalResult;
    }

    public LocalDateTime getPassTime() {
        return passTime;
    }

    public void setPassTime(LocalDateTime passTime) {
        this.passTime = passTime;
    }

    public LocalDate getExpireTime() {
        return expireTime;
    }

    public void setExpireTime(LocalDate expireTime) {
        this.expireTime = expireTime;
    }

    @Override
    protected String prefix() {
        return "RR";
    }

    public RateLevel getApproveRate() {
        return approveRate;
    }

    public void setApproveRate(RateLevel approveRate) {
        this.approveRate = approveRate;
    }

    public String getApproveScore() {
        return approveScore;
    }

    public void setApproveScore(String approveScore) {
        this.approveScore = approveScore;
    }

    public WhetherState getCallZyRisk() {
        return callZyRisk;
    }

    public void setCallZyRisk(WhetherState callZyRisk) {
        this.callZyRisk = callZyRisk;
    }

    public String getApplyChannel() {
        return applyChannel;
    }

    public void setApplyChannel(String applyChannel) {
        this.applyChannel = applyChannel;
    }

    public MarketingChannel getMarketingChannel() {
        return marketingChannel;
    }

    public void setMarketingChannel(MarketingChannel marketingChannel) {
        this.marketingChannel = marketingChannel;
    }

    public String getPolicyName() {
        return policyName;
    }

    public void setPolicyName(String policyName) {
        this.policyName = policyName;
    }

    public RightsPayType getRightsPayType() {
        return rightsPayType;
    }

    public void setRightsPayType(RightsPayType rightsPayType) {
        this.rightsPayType = rightsPayType;
    }

    public String getPipelineId() {
        return pipelineId;
    }

    public void setPipelineId(String pipelineId) {
        this.pipelineId = pipelineId;
    }

    public RiskChannel getRiskChannel() {
        return riskChannel;
    }

    public void setRiskChannel(RiskChannel riskChannel) {
        this.riskChannel = riskChannel;
    }

    //项目唯一编码 add by yunhengtong 20250821 start
    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode;
    }
    //目唯一编码 add by yunhengtong 20250821 end
}
