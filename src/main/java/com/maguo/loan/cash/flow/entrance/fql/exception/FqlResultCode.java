package com.maguo.loan.cash.flow.entrance.fql.exception;

import java.util.Objects;

/**
 * <AUTHOR>
 * @Description 公共响应码
 * @Date 2024/3/21 14:27
 * @Version v1.0
 **/
public enum FqlResultCode {

    SUCCESS("001", "请求成功"),
    FAILURE ("002", "请求失败-服务调用异常"),
    SYSTEM_ERROR("1000", "请求异常（未知错误）"),
    INVALID_PARAM("4001", "参数错误"),
    SIGN_VERIFY_FAIL("4002", "验签失败"),
    NOT_SUPPORT_CHANNEL("4003", "未知渠道号（channlB）"),
    NOT_SUPPORT_SERVICE("4004", "未知服务号（serviceId）");

    private String code;
    private String msg;

    FqlResultCode(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public FqlResultCode getByCode(String code) {
        for (FqlResultCode resultCode : FqlResultCode.values()) {
            if (Objects.equals(resultCode.getCode(), code)) {
                return resultCode;
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }
}
