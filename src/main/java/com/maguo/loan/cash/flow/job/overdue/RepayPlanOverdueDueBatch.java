package com.maguo.loan.cash.flow.job.overdue;


import com.jinghang.cash.api.dto.ProjectElementsDto;
import com.jinghang.cash.api.dto.ProjectElementsExtDto;
import com.jinghang.cash.api.dto.ProjectInfoDto;
import com.jinghang.common.util.CollectionUtil;
import com.jinghang.common.util.JsonUtil;
import com.maguo.loan.cash.flow.dto.CallBackDTO;
import com.maguo.loan.cash.flow.entity.Loan;
import com.maguo.loan.cash.flow.entity.RepayPlan;
import com.maguo.loan.cash.flow.entrance.common.service.ProjectInfoService;
import com.maguo.loan.cash.flow.enums.CallbackState;
import com.maguo.loan.cash.flow.enums.IsIncludingEquity;
import com.maguo.loan.cash.flow.enums.RepayState;
import com.maguo.loan.cash.flow.job.AbstractJobHandler;
import com.maguo.loan.cash.flow.job.JobParam;
import com.maguo.loan.cash.flow.repository.LoanRepository;
import com.maguo.loan.cash.flow.repository.RepayPlanRepository;
import com.maguo.loan.cash.flow.service.MqService;
import com.maguo.loan.cash.flow.util.AmountUtil;
import com.maguo.loan.cash.flow.util.DateUtil;

import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Comparator;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * @Classname RepayPlanOverdueDueBatch
 * @Description 还款计划逾期处理
 * @Date 2023/10/26 18:05
 * @Created by gale
 */
@Component
@JobHandler("repayPlanOverdueDueBatch")
public class RepayPlanOverdueDueBatch extends AbstractJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(RepayPlanOverdueDueBatch.class);

    /**
     * 咨询费罚息费率 (36%年化利率)
     */
    public static final BigDecimal CONSULT_OVERDUE_RATE = new BigDecimal("0.0985");

    /**
     * 罚息费率为：23.99%/360的资方，咨询费罚息费率 (24%年化利率)
     */
    public static final BigDecimal SPECIAL_CONSULT_OVERDUE_RATE = new BigDecimal("0.067");

    private static final BigDecimal PERCENT = BigDecimal.valueOf(100);

    @Autowired
    private MqService mqService;

    @Autowired
    private RepayPlanRepository repayPlanRepository;

    private static final Integer PAGE_SIZE = 5000;
    @Autowired
    private LoanRepository loanRepository;

    @Autowired
    private ProjectInfoService projectInfoService;

    @Override
    public void doJob(JobParam jobParam) {
        logger.info("RepayPlanOverdueDueBatch jobParam:{}", JsonUtil.toJsonString(jobParam));

        LocalDate date;
        List<String> loanIds = null;
        if (jobParam == null) {
            date = LocalDate.now();
        } else {
            date = jobParam.getStartDate() == null ? LocalDate.now() : jobParam.getStartDate();
            loanIds = jobParam.getLoanIds();
        }

        AtomicInteger pageNumber = new AtomicInteger(0);


        while (true) {
            //分页查询还款计划
            PageRequest pageRequest = PageRequest.of(pageNumber.getAndIncrement(), PAGE_SIZE, Sort.by(Sort.Direction.ASC, "id"));
            List<RepayPlan> repayPlanList = null;
            //若loanIds为空：查询所有 "计划还款日期在date之前" 且 "客户还款状态为正常 (NORMAL)" 的还款计划。
            if (CollectionUtil.isEmpty(loanIds)) {
                repayPlanList = repayPlanRepository.findByPlanRepayDateBeforeAndCustRepayState(
                    date, RepayState.NORMAL, pageRequest);
            } else {
                //若loanIds非空：查询指定贷款 ID 列表的还款计划。
                repayPlanList = repayPlanRepository.findByLoanIdIn(new HashSet<>(loanIds));
            }

            if (repayPlanList.isEmpty()) {
                break;
            }
            //对查询到的还款计划按贷款 ID 分组，并对每组按期数 (period) 升序排序
            Map<String, List<RepayPlan>> repayPlanMaps = repayPlanList.stream().collect(Collectors.groupingBy(RepayPlan::getLoanId, Collectors.collectingAndThen(
                Collectors.toList(),
                list -> list.stream()
                    .sorted(Comparator.comparingInt(RepayPlan::getPeriod))
                    .collect(Collectors.toList())
            )));

            for (Map.Entry<String, List<RepayPlan>> entry : repayPlanMaps.entrySet()) {
                List<RepayPlan> loanRepayPlanList = entry.getValue();
                Loan loan = loanRepository.findById(entry.getKey()).orElseThrow();
                logger.info("开始处理贷款 {} 的 {} 期还款计划", loan.getId(), loanRepayPlanList.size());

                //权益客户不用跑罚息
                if (IsIncludingEquity.Y.equals(loan.getIsIncludingEquity())) {
                    continue;
                }

                // 根据项目编码获取产品要素扩展信息
                ProjectInfoDto projectInfoVO = projectInfoService.queryProjectInfo(loan.getProjectCode());
                ProjectElementsExtDto projectElementsExt = projectInfoVO.getElementsExt();
                if (projectElementsExt == null) {
                    logger.warn("未找到项目要素扩展信息，loanId: {}, projectCode: {}", loan.getId(), loan.getProjectCode());
                    continue;
                }

                // 获取宽限期类型和天数
                String gracePeriodType = projectElementsExt.getGracePeriodType().getCode();
                int gracePeriodDays = 0;
                try {
                    // 使用calculateAdjustedGracePeriodDays方法计算可能经过年结期间调整的宽限期天数
                    gracePeriodDays = calculateAdjustedGracePeriodDays(projectInfoVO,
                        Integer.parseInt(projectElementsExt.getGracePeriodDays()), date);
                } catch (NumberFormatException e) {
                    logger.warn("宽限期天数格式错误，loanId: {}, gracePeriodDays: {}", loan.getId(), projectElementsExt.getGracePeriodDays());
                    continue;
                }


                // 循环每一期还款计划，计算逾期天数 (overDay)：通过DateUtil.dateDiff计算计划还款日期与基准日期的差值。
                for (int i = 0; i < loanRepayPlanList.size(); i++) {
                    RepayPlan repayPlan = loanRepayPlanList.get(i);
                    //逾期天数
                    long overDay = DateUtil.dateDiff(repayPlan.getPlanRepayDate(), date);

                    // 根据宽限期类型判断是否需要宽限期
                    boolean needSkipForGracePeriod = false;
                    if ("SQ".equals(gracePeriodType)) {
                        // 首期宽限期类型：只有第一期(i==0)有宽限期
                        if (i == 0 && overDay <= gracePeriodDays) {
                            needSkipForGracePeriod = true;
                            logger.info("贷款 {} 第 {} 期处于首期宽限期内 (逾期天数: {} <= 宽限天数: {})，跳过罚息计算",
                                loan.getId(), repayPlan.getPeriod(), overDay, gracePeriodDays);
                        }
                    } else if ("MQ".equals(gracePeriodType)) {
                        // 每期宽限期类型：每期都有宽限期
                        if (overDay <= gracePeriodDays) {
                            needSkipForGracePeriod = true;
                            logger.info("贷款 {} 第 {} 期处于每期宽限期内 (逾期天数: {} <= 宽限天数: {})，跳过罚息计算",
                                loan.getId(), repayPlan.getPeriod(), overDay, gracePeriodDays);
                        }
                    }

                    if (needSkipForGracePeriod) {
                        continue;
                    }

                    //应还罚息
                    BigDecimal penaltyAmt = totalAmtPenaltyAmt(loan, repayPlan, overDay);
                    //若罚息为 0，跳过
                    if (BigDecimal.ZERO.compareTo(penaltyAmt) == 0) {
                        logger.info("贷款 {} 第 {} 期计算罚息为0，跳过处理", loan.getId(), repayPlan.getPeriod());
                        continue;
                    }

                    logger.info("贷款 {} 第 {} 期产生罚息: {} (逾期天数: {}, 剩余本金: {})", loan.getId(), repayPlan.getPeriod(), penaltyAmt, overDay, AmountUtil.subtract(repayPlan.getPrincipalAmt(), repayPlan.getActPrincipalAmt()));

                    //更新还款计划的罚息金额：累计已有的实际罚息 (actPenaltyAmt)
                    repayPlan.setPenaltyAmt(penaltyAmt.add(safeNum(repayPlan.getActPenaltyAmt())));
                    //更新总金额 (amount)：本金 + 利息 + 罚息 + 担保费 + 咨询费的总和 (通过AmountUtil.sum计算)。
                    repayPlan.setAmount(
                        AmountUtil.sum(repayPlan.getPrincipalAmt(), repayPlan.getInterestAmt(),
                            repayPlan.getPenaltyAmt(), repayPlan.getGuaranteeAmt(), repayPlan.getConsultFee()));
                    repayPlanRepository.save(repayPlan);

                    billCallback(repayPlan, loan);
                }
            }
        }
    }

    /**
     * 逾期回调
     *
     * @param repayPlan 逾期的还款计划
     * @param loan      关联的贷款
     */
    private void billCallback(RepayPlan repayPlan, Loan loan) {
        String repayPlanId = repayPlan.getId();

        CallBackDTO callBackDTO = new CallBackDTO();
        callBackDTO.setFlowChannel(loan.getFlowChannel());
        callBackDTO.setBusinessId(repayPlanId);
        callBackDTO.setCallbackState(CallbackState.OVERDUE);
        mqService.submitCallbackCommonNotify(JsonUtil.toJsonString(callBackDTO));

    }

    /**
     * 计算对客罚息
     *
     * @param loan      贷款信息
     * @param repayPlan 还款计划
     * @param overDay   逾期天数
     * @return 计算出的罚息金额
     */
    private BigDecimal totalAmtPenaltyAmt(Loan loan, RepayPlan repayPlan, long overDay) {
        //剩余未还本金
        BigDecimal totalAmtBase = AmountUtil.subtract(repayPlan.getPrincipalAmt(), repayPlan.getActPrincipalAmt());
        if (totalAmtBase.compareTo(BigDecimal.ZERO) <= 0) {
            return BigDecimal.ZERO;
        }
        BigDecimal consultOverdueRate = CONSULT_OVERDUE_RATE;
        return totalAmtBase.multiply(new BigDecimal(overDay)).multiply(consultOverdueRate.divide(PERCENT)).setScale(2, RoundingMode.HALF_UP);
    }

    /**
     * 安全获取数值，避免空指针
     *
     * @param num 可能为空的数值
     * @return 若为空则返回0，否则返回原值
     */
    private BigDecimal safeNum(BigDecimal num) {
        return num == null ? BigDecimal.ZERO : num;
    }

    /**
     * 计算年结期间调整后的宽限期天数
     * 根据项目要素配置判断是否在年结期间，如果年结顺延且当前在年结期间，则增加宽限期天数
     *
     * @param projectInfoVO 项目信息
     * @param baseGraceDays 基础宽限期天数
     * @param date          基准日期
     * @return 调整后的宽限期天数
     */
    private int calculateAdjustedGracePeriodDays(ProjectInfoDto projectInfoVO, int baseGraceDays, LocalDate date) {
        int adjustedGraceDays = baseGraceDays;
        ProjectElementsDto projectElements = projectInfoVO.getElements();
        // 检查是否启用年结顺延
        if (projectElements != null && projectElements.getGraceNext() != null
            && "Y".equals(projectElements.getGraceNext())) {

            // 检查是否在年结期间
            LocalDateTime tempStartTime = projectElements.getTempStartTime();
            LocalDateTime tempEndTime = projectElements.getTempEndTime();

            if (tempStartTime != null && tempEndTime != null) {
                LocalDate startDate = tempStartTime.toLocalDate();
                LocalDate endDate = tempEndTime.toLocalDate();

                // 判断基准日期是否在年结期间内
                if (!date.isBefore(startDate) && !date.isAfter(endDate)) {
                    // 在年结期间内，增加宽限期天数
                    long additionalDays = DateUtil.dateDiff(startDate, endDate) + 1;
                    adjustedGraceDays += (int) additionalDays;
                    logger.info("在年结期间内，增加宽限期天数。基础宽限期: {}, 增加天数: {}, 调整后宽限期: {}",
                        baseGraceDays, additionalDays, adjustedGraceDays);
                }
            }
        }

        return adjustedGraceDays;
    }
}
