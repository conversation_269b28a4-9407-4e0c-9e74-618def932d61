package com.maguo.loan.cash.flow.entrance.fql.exception;

/**
 * 异常类
 *
 * <AUTHOR>
 */
public class FenQiLeBizException extends RuntimeException {

    private final FenQiLeResultCode resultCode;

    public FenQiLeBizException(String message) {
        super(message);
        this.resultCode = FenQiLeResultCode.SYSTEM_ERROR;
    }

    public FenQiLeBizException(FenQiLeResultCode resultCode) {
        super(resultCode.getMsg());
        this.resultCode = resultCode;
    }

    public FenQiLeBizException(String message, FenQiLeResultCode resultCode) {
        super(message);
        this.resultCode = resultCode;
    }

    public FenQiLeResultCode getResultCode() {
        return resultCode;
    }

}
