package com.maguo.loan.cash.flow.entrance.fql.exception;

/**
 * <AUTHOR>
 * @Description 拍拍异常code
 * @Date 2024/12/30 11:26
 **/
public enum FqlErrorCode {

    ER00("系统异常（未知异常）"),

    ER01("参数错误"),

    ER11("风控拒绝"),

    ER10("人脸识别失败拒绝"),

    ER12("用户信息异常——身份证异常拒绝"),

    ER12_OTHER("用户信息异常——其他校验失败拒绝"),

    ER13("银行卡-用户信息校验拒绝"),

    ER13_LIMIT_EXCEEDED("银行卡-二、三类账户交易金额超限拒绝"),

    ER13_OTHER("银行卡-其他拒绝"),

    ER14("在贷余额/历史逾期/总授信/其他渠道授信不满足要求"),

    ER15("其他进件信息不满足要求"),

    ER16("可用额度不足"),

    ER17("保证金/融担不足");

    private final String description;

    FqlErrorCode(String description) {
        this.description = description;
    }

    /**
     * Gets the description of the error code.
     *
     * @return the error description
     */
    public String getDescription() {
        return description;
    }
}
