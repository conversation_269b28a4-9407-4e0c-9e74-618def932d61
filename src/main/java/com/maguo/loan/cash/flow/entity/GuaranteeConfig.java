package com.maguo.loan.cash.flow.entity;

import com.jinghang.common.util.StringUtil;
import com.maguo.loan.cash.flow.enums.AbleStatus;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;

import java.io.Serial;
import java.time.LocalDateTime;

/**
 * 融担方主数据表
 *
 * @Author: Lior
 * @CreateTime: 2025/8/18 11:25
 */
@Entity
@Table(name = "guarantee_config")
public class GuaranteeConfig extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1234567890123456789L;

    /**
     * 融担方编码
     */
    @Column(name = "guarantee_code")
    private String guaranteeCode;

    /**
     * 融担方主体全称
     */
    @Column(name = "guarantee_name")
    private String guaranteeName;

    /**
     * 融担方简称
     */
    @Column(name = "guarantee_name_short")
    private String guaranteeNameShort;

    /**
     * 融担方简介
     */
    @Column(name = "guarantee_desc")
    private String guaranteeDesc;

    /**
     * 联系人
     */
    @Column(name = "contact_person")
    private String contactPerson;

    /**
     * 联系电话
     */
    @Column(name = "contact_phone")
    private String contactPhone;

    /**
     * 邮箱地址
     */
    @Column(name = "email_address")
    private String emailAddress;

    /**
     * 启用状态
     */
    @Enumerated(EnumType.STRING)
    private AbleStatus enabled;

    public String getGuaranteeCode() {
        return guaranteeCode;
    }

    public void setGuaranteeCode(String guaranteeCode) {
        this.guaranteeCode = guaranteeCode;
    }

    public String getGuaranteeName() {
        return guaranteeName;
    }

    public void setGuaranteeName(String guaranteeName) {
        this.guaranteeName = guaranteeName;
    }

    public String getGuaranteeNameShort() {
        return guaranteeNameShort;
    }

    public void setGuaranteeNameShort(String guaranteeNameShort) {
        this.guaranteeNameShort = guaranteeNameShort;
    }

    public String getGuaranteeDesc() {
        return guaranteeDesc;
    }

    public void setGuaranteeDesc(String guaranteeDesc) {
        this.guaranteeDesc = guaranteeDesc;
    }

    public String getContactPerson() {
        return contactPerson;
    }

    public void setContactPerson(String contactPerson) {
        this.contactPerson = contactPerson;
    }

    public String getContactPhone() {
        return contactPhone;
    }

    public void setContactPhone(String contactPhone) {
        this.contactPhone = contactPhone;
    }

    public String getEmailAddress() {
        return emailAddress;
    }

    public void setEmailAddress(String emailAddress) {
        this.emailAddress = emailAddress;
    }

    public AbleStatus getEnabled() {
        return enabled;
    }

    public void setEnabled(AbleStatus enabled) {
        this.enabled = enabled;
    }

    @Override
    protected String prefix() {
        return "GC";
    }

    @Override
    public void prePersist() {
        if (StringUtil.isBlank(super.getId())) {
            setId(genId());
        }
        super.setCreatedBy("sys");
        super.setCreatedTime(LocalDateTime.now());
    }
}
