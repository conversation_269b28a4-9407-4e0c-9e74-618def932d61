package com.maguo.loan.cash.flow.entrance.fql.filter;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.google.gson.Gson;
import com.jinghang.common.util.JsonUtil;
import com.maguo.loan.cash.flow.entrance.fql.config.FqlConfig;
import com.maguo.loan.cash.flow.entrance.fql.dto.FqlCommonRequest;
import com.maguo.loan.cash.flow.entrance.fql.dto.FqlCommonResponse;
import com.maguo.loan.cash.flow.entrance.fql.exception.FqlBizException;
import com.maguo.loan.cash.flow.entrance.fql.exception.FqlResultCode;
import com.maguo.loan.cash.flow.entrance.fql.filter.sdk.LxClient;
import com.maguo.loan.cash.flow.entrance.fql.filter.sdk.LxRequest;
import com.maguo.loan.cash.flow.entrance.fql.filter.sdk.LxResponse;
import com.maguo.loan.cash.flow.entrance.fql.filter.utils.EncryptUtils;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class Demo {

    //分期乐公钥(加密解密用)
    private static String lxPublicKey = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCN6w4G1gZnUdEAH1bNO6JpnwsyTCWpkwOMaOLDXYGM1TcnX1SF1FrdjVlRyf1iPiSZgbFy0+bjkwwZ9ko8+ygYaSiREuNV7wN6eY2SdOpfYm1CB3UM56Q7Wh2gwgiq28CHE1f24qsGnq8fe2gUti1WNXDiDpMf8YsMQkda1qVSMwIDAQAB";

    //分期乐私钥(加密解密用)
    private static  String lxPrivateKey = "MIICdQIBADANBgkqhkiG9w0BAQEFAASCAl8wggJbAgEAAoGBAI3rDgbWBmdR0QAfVs07ommfCzJMJamTA4xo4sNdgYzVNydfVIXUWt2NWVHJ/WI+JJmBsXLT5uOTDBn2Sjz7KBhpKJES41XvA3p5jZJ06l9ibUIHdQznpDtaHaDCCKrbwIcTV/biqwaerx97aBS2LVY1cOIOkx/xiwxCR1rWpVIzAgMBAAECgYAW/5MP14pFHxSkFPAPpUzeGfiKB54I23q1O+AXseUHKhxchI3hfDbXdYsWfonR7Gko+UQE7EMb+R36bRe9xyPJhO94AZRaAqrSqLzA05SGy9o5AgODF50TEq3fmTeUr7tqI9Z3X48pLTrj5rK0Aip07cxYSeZFT5inF1F5cMjB7QJBAJH2oAoPlDD1AvwZlV3rsx1k6TZ2PJT6IVHdUO8CtKgv85Yz4oNVdI6/o+tkt6rPoldhYzRAyPK9c9wpl9Ihm3cCQQD457/09/is+4yOR67vgOAb4tT42fSoOxWv9Vc1yVRhrIQg18jH1nZ3JgomSf8HVFLgpSg9dhfxDW5clNfNJ3YlAkB/Ge8KU76LUw8iT3TNsCzsb67Uiej/IUBadQnA1u5Rdk/6f3vDOWqh0yb+F1oIF34Z8Ofd3HwfQ/HbySw3AqTJAkABm0VS6HLTcwAyPuYzJ4rKw0kKniWFHHSEYToB393Kj5l+uMXT/F9hX357Rsj9nB640ngTBXscFDEdKdzGtzV9AkAKiLE8PrkES5cSg7F8+gMWVYugwUGMsjQ99dgJXPcd1cDpJVZ4ToyX4/WWs7ZCndhh3cYNh5lXNOFMQdCM5fsM";

    //分期乐公钥(加签解签用)
    private static String lxSignPublicKey = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCN6w4G1gZnUdEAH1bNO6JpnwsyTCWpkwOMaOLDXYGM1TcnX1SF1FrdjVlRyf1iPiSZgbFy0+bjkwwZ9ko8+ygYaSiREuNV7wN6eY2SdOpfYm1CB3UM56Q7Wh2gwgiq28CHE1f24qsGnq8fe2gUti1WNXDiDpMf8YsMQkda1qVSMwIDAQAB";

    //分期乐私钥(加签解签用)
    private static  String lxSignPrivateKey = "MIICdQIBADANBgkqhkiG9w0BAQEFAASCAl8wggJbAgEAAoGBAI3rDgbWBmdR0QAfVs07ommfCzJMJamTA4xo4sNdgYzVNydfVIXUWt2NWVHJ/WI+JJmBsXLT5uOTDBn2Sjz7KBhpKJES41XvA3p5jZJ06l9ibUIHdQznpDtaHaDCCKrbwIcTV/biqwaerx97aBS2LVY1cOIOkx/xiwxCR1rWpVIzAgMBAAECgYAW/5MP14pFHxSkFPAPpUzeGfiKB54I23q1O+AXseUHKhxchI3hfDbXdYsWfonR7Gko+UQE7EMb+R36bRe9xyPJhO94AZRaAqrSqLzA05SGy9o5AgODF50TEq3fmTeUr7tqI9Z3X48pLTrj5rK0Aip07cxYSeZFT5inF1F5cMjB7QJBAJH2oAoPlDD1AvwZlV3rsx1k6TZ2PJT6IVHdUO8CtKgv85Yz4oNVdI6/o+tkt6rPoldhYzRAyPK9c9wpl9Ihm3cCQQD457/09/is+4yOR67vgOAb4tT42fSoOxWv9Vc1yVRhrIQg18jH1nZ3JgomSf8HVFLgpSg9dhfxDW5clNfNJ3YlAkB/Ge8KU76LUw8iT3TNsCzsb67Uiej/IUBadQnA1u5Rdk/6f3vDOWqh0yb+F1oIF34Z8Ofd3HwfQ/HbySw3AqTJAkABm0VS6HLTcwAyPuYzJ4rKw0kKniWFHHSEYToB393Kj5l+uMXT/F9hX357Rsj9nB640ngTBXscFDEdKdzGtzV9AkAKiLE8PrkES5cSg7F8+gMWVYugwUGMsjQ99dgJXPcd1cDpJVZ4ToyX4/WWs7ZCndhh3cYNh5lXNOFMQdCM5fsM";



    //合作方公钥(加密解密用)
    private static  String partnerPublicKey = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCx4JXvefXa1ESBAb03MXE9a+2Od4Xhu8ivK8vGjrmP33OB6yfUP3hLGMHIsteryLBXUYyDJQNiuxE8wNDIVOS1S8Okl3qnS63pIqy1rbMXVo5YC78Liq5Prd+MWggxk554BGVLQLaXeMWfn6VCmZmS4+MsgFVAe9xBhWIk0EXYswIDAQAB";
    //合作方私钥(加密解密用)
    private static  String partnerPrivateKey = "MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBALHgle959drURIEBvTcxcT1r7Y53heG7yK8ry8aOuY/fc4HrJ9Q/eEsYwciy16vIsFdRjIMlA2K7ETzA0MhU5LVLw6SXeqdLrekirLWtsxdWjlgLvwuKrk+t34xaCDGTnngEZUtAtpd4xZ+fpUKZmZLj4yyAVUB73EGFYiTQRdizAgMBAAECgYAEaEapcjGpHuT691KQuV0PbbWrTS3YNoFrSbVZBvSfv88fsyEVArpAeMK7GNH8M9L0Duq23dC+064X0xqOjtVDLW7WCTi5bxUrkmwxozU8pEu0/7eq9UM/P/fx5onwFjEW0KqGcR9aUdVPKh+BRrFwiZA9r3pbxx9TVYAzp5y/ZQJBAOkeRYtH94WyWoDg73TUGlPFINwQ2YykGRMJ43Sgdnw7FwQAYo7+NhIacACaJUmxUijuRSJO6Re3K1NYJEbKTy0CQQDDVjwr2E0qmbx3vWskKN8u7Q+nY/4VVwpMB9cDBEJWBupGoHv4WrEkuUUZTUPn0rks+UdecDC+R290JFui7rNfAkEAoqvBE6QwkVcX2H8eGYQ4quQQPgB0DrQj2yk3U5b1l1MUiHJMVEQILzHLnl/yTS4ziuRZ0csG1Mm4rfv/tHZQMQJAXGKoysPem0tiy+8WgV+jTvpn8O9l+InWIOeEVbTp+u4CV60HdQrPxWKqv7C/cSFE23R6wLunEhePKwsXHBRxWQJBALFC2Y9uCp0r2apb+Yne+dw5cBm5uXkpMcNO20C0Y+gEXCEMUcPvrfJo3oQHmzg2GfHT/P1GDgTqycwO8SEeZxw=";

    //合作方公钥(加签解签用)
    private static  String partnerSignPublicKey = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCx4JXvefXa1ESBAb03MXE9a+2Od4Xhu8ivK8vGjrmP33OB6yfUP3hLGMHIsteryLBXUYyDJQNiuxE8wNDIVOS1S8Okl3qnS63pIqy1rbMXVo5YC78Liq5Prd+MWggxk554BGVLQLaXeMWfn6VCmZmS4+MsgFVAe9xBhWIk0EXYswIDAQAB";
    //合作方私钥(加签解签用)
    private static  String partnerSignPrivateKey = "MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBALHgle959drURIEBvTcxcT1r7Y53heG7yK8ry8aOuY/fc4HrJ9Q/eEsYwciy16vIsFdRjIMlA2K7ETzA0MhU5LVLw6SXeqdLrekirLWtsxdWjlgLvwuKrk+t34xaCDGTnngEZUtAtpd4xZ+fpUKZmZLj4yyAVUB73EGFYiTQRdizAgMBAAECgYAEaEapcjGpHuT691KQuV0PbbWrTS3YNoFrSbVZBvSfv88fsyEVArpAeMK7GNH8M9L0Duq23dC+064X0xqOjtVDLW7WCTi5bxUrkmwxozU8pEu0/7eq9UM/P/fx5onwFjEW0KqGcR9aUdVPKh+BRrFwiZA9r3pbxx9TVYAzp5y/ZQJBAOkeRYtH94WyWoDg73TUGlPFINwQ2YykGRMJ43Sgdnw7FwQAYo7+NhIacACaJUmxUijuRSJO6Re3K1NYJEbKTy0CQQDDVjwr2E0qmbx3vWskKN8u7Q+nY/4VVwpMB9cDBEJWBupGoHv4WrEkuUUZTUPn0rks+UdecDC+R290JFui7rNfAkEAoqvBE6QwkVcX2H8eGYQ4quQQPgB0DrQj2yk3U5b1l1MUiHJMVEQILzHLnl/yTS4ziuRZ0csG1Mm4rfv/tHZQMQJAXGKoysPem0tiy+8WgV+jTvpn8O9l+InWIOeEVbTp+u4CV60HdQrPxWKqv7C/cSFE23R6wLunEhePKwsXHBRxWQJBALFC2Y9uCp0r2apb+Yne+dw5cBm5uXkpMcNO20C0Y+gEXCEMUcPvrfJo3oQHmzg2GfHT/P1GDgTqycwO8SEeZxw=";




    public static void main(String[] args) {

        String s3 = bulidbwRequest();
        System.out.println(s3);

        String s2 = bulidLxRequest();
        System.out.println(s2);

        String s4 = bulidbwRequest2();
        System.out.println(s4);
        LxClient lxClient = new LxClient();
        FqlCommonRequest response = new FqlCommonRequest();
//        response.setCode("001");
//        response.setMsg("请求成功");
        response.setPartnerCode("FQLQY001");
        response.setTimestamp("2025-08-19 14:29:02");
        response.setBizContent("Y8tXhYSh+6T135QyVihBOLmzAdI7LPJQ8LL1k9JD45YbPmPucZy2Dmk87m/YX7FpwMHceHqUOMqOsPNmG84Rb8E2MKXzpKsm5YgaeZXRhWGkHElpTOkRym3ArpkF3QUHmTHVBwTyoOS4oBQPDOHFIsuMKTuMJENm8atb/Q1yAoo=");
        Gson gson = new Gson();
        String s1 = gson.toJson(response);
        String s = String.valueOf(s1);
        com.alibaba.fastjson.JSONObject request = JSON.parseObject(s);
        String sign = "dspeLYrsHIT/ed+y7NcwV36U9z63lTrDL3y7aNg5eVp4p+WaiMBIakpBnsrNCBQP81lNJKIdy/1UD4QZuWCzXS04Vb5e5ZibA0dG+RiqY0t5DQMi2sKqtKiFt+3pv+ngulv8dRVFWlGYLhR4sZmYhQHmLKNnmftrTr6GPCgjnVE=";
        String data = JSON.toJSONString(request, SerializerFeature.MapSortField);
        boolean checkSign = lxClient.verifySign(data, sign, partnerSignPublicKey);
        if (!checkSign) {
            throw new FqlBizException(FqlResultCode.SIGN_VERIFY_FAIL);
        }
        try {
           String decrptString = lxClient.decrypt(request.getString("bizContent"),lxPrivateKey);
            System.out.println(decrptString);
        } catch (Exception e) {
            throw new FqlBizException(FqlResultCode.SIGN_VERIFY_FAIL);
        }
    }
    public static String bulidLxRequest(){
        //分期乐请求合作方
        LxClient lxClient = new LxClient();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("applyId","1111111111111111111");
        jsonObject.put("partnerCode","FQLQY001");
        //明文数据转json
        String bizContent = JSON.toJSONString(jsonObject);

        //构建请求数据
        FqlCommonRequest lxRequest = new FqlCommonRequest();
        lxRequest.setPartnerCode("FQLQY001");
        lxRequest.setTimestamp(new SimpleDateFormat("yyyy-MM-dd hh:mm:ss").format(new Date()));
        lxRequest.setBizContent(bizContent);
        //先对请求数据进行加密
        String encrptString = lxClient.encrypt(bizContent,partnerPublicKey);
        System.out.println("乐信侧对报文体内容加密后字符串:" + encrptString);
        lxRequest.setBizContent(encrptString);
        //对整个请求对象进行加签,key排序
        String data = JSON.toJSONString(lxRequest, SerializerFeature.MapSortField);
        String sign = lxClient.sign(data,lxSignPrivateKey);
        System.out.println("乐信侧对发送对象加签之后的字符串:" + sign);
        //对请求数据进行加签
        lxRequest.setSign(sign);
        //构建发送的请求字符串
        String requestStr = JSON.toJSONString(lxRequest, SerializerFeature.MapSortField);
        System.out.println("乐信侧发送请求字符串:" + requestStr);
        return requestStr;
    }

    public static String bulidbwRequest(){

        //合作方返回百维
        LxClient lxClient = new LxClient();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("applyId","1111111111111111111");
        jsonObject.put("partnerCode","FQLQY001");
        //明文数据转json
        String bizContent = JSON.toJSONString(jsonObject);

        //构建请求数据
        FqlCommonResponse lxRequest = new FqlCommonResponse();
        lxRequest.setCode("001");
        lxRequest.setMsg("请求成功");
        lxRequest.setTimestamp(new SimpleDateFormat("yyyy-MM-dd hh:mm:ss").format(new Date()));
        //先对请求数据进行加密
        String encrptString = lxClient.encrypt(bizContent,partnerPublicKey);
        System.out.println("合作方侧对报文体内容加密后字符串:" + encrptString);
        lxRequest.setBizContent(encrptString);
        //对整个请求对象进行加签,key排序
        String data = JSON.toJSONString(lxRequest, SerializerFeature.MapSortField);
        String sign = lxClient.sign(data,lxSignPrivateKey);
        System.out.println("合作方侧对发送对象加签之后的字符串:" + sign);
        //对请求数据进行加签
        lxRequest.setSign(sign);
        //构建发送的请求字符串
        String requestStr = JSON.toJSONString(lxRequest, SerializerFeature.MapSortField);
        System.out.println("合作方侧发送请求字符串:" + requestStr);

        EncryptUtils encryptUtils = new EncryptUtils(new FqlConfig());
        String s = encryptUtils.decryptUtils(lxRequest);
        System.out.println("百维解密后------------------------------------"+s);
        return requestStr;
    }

    public static String bulidbwRequest2(){
        //合作方返回百维
        LxClient lxClient = new LxClient();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("applyId","1111111111111111111");
        jsonObject.put("partnerCode","FQLQY001");
        //明文数据转json
        String bizContent = JSON.toJSONString(jsonObject);


        EncryptUtils encryptUtils = new EncryptUtils(new FqlConfig());
        FqlCommonRequest fqlCommonRequest = encryptUtils.encryptUtils(jsonObject);
        String s = JSON.toJSONString(fqlCommonRequest);
        System.out.println("加密后内容===================================="+s);

        Gson gson = new Gson();
        String s1 = gson.toJson(fqlCommonRequest);
        String s4 = String.valueOf(s1);
        com.alibaba.fastjson.JSONObject request = JSON.parseObject(s4);
        String sign = (String) request.get("sign");
        request.remove("sign");
        String data = JSON.toJSONString(request, SerializerFeature.MapSortField);
        boolean checkSign = lxClient.verifySign(data, sign, partnerSignPublicKey);
        if (!checkSign) {
            throw new FqlBizException(FqlResultCode.SIGN_VERIFY_FAIL);
        }
        try {
            String decrptString = lxClient.decrypt(request.getString("bizContent"),lxPrivateKey);
            System.out.println(decrptString);
        } catch (Exception e) {
            throw new FqlBizException(FqlResultCode.SIGN_VERIFY_FAIL);
        }
      return "1111111111111111111111";
    }
}
