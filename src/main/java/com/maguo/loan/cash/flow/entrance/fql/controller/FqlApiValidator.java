package com.maguo.loan.cash.flow.entrance.fql.controller;

import com.maguo.loan.cash.flow.entrance.ppd.common.ResultCode;
import com.maguo.loan.cash.flow.entrance.ppd.common.ValidationException;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;

/**
 * <AUTHOR>
 */
public class FqlApiValidator {

    public static void validate(BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            StringBuilder sb = new StringBuilder();
            for (FieldError fieldError : bindingResult.getFieldErrors()) {
                sb.append(fieldError.getField()).append(":").append(fieldError.getDefaultMessage()).append(";");
            }
            throw new ValidationException(sb.toString(), ResultCode.PARAM_ILLEGAL);
        }
    }

}
