package com.maguo.loan.cash.flow.entrance.fql.dto.repay;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Description 还款试算响应参数
 * @Date 2025-08-06
 * @Version 1.0
 */
public class FenQiLeRepayTrialResponse {

    /**
     * 接口返回码
     * 0-成功；
     * 1-失败；
     * 2-异常；
     * 3-查无此单；
     * 4-已结清
     */
    private String code;

    /**
     * 错误描述 code为1,2时必传
     */
    private String msg;

    /**
     * 本金(元)
     */
    private BigDecimal repayPrincipal;

    /**
     * 利息(元)
     */
    private BigDecimal repayFee;

    /**
     * 罚息(元)
     */
    private BigDecimal repayMuclt;

    /**
     * 总金额(元)
     */
    private BigDecimal repayTotal;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public BigDecimal getRepayPrincipal() {
        return repayPrincipal;
    }

    public void setRepayPrincipal(BigDecimal repayPrincipal) {
        this.repayPrincipal = repayPrincipal;
    }

    public BigDecimal getRepayFee() {
        return repayFee;
    }

    public void setRepayFee(BigDecimal repayFee) {
        this.repayFee = repayFee;
    }

    public BigDecimal getRepayMuclt() {
        return repayMuclt;
    }

    public void setRepayMuclt(BigDecimal repayMuclt) {
        this.repayMuclt = repayMuclt;
    }

    public BigDecimal getRepayTotal() {
        return repayTotal;
    }

    public void setRepayTotal(BigDecimal repayTotal) {
        this.repayTotal = repayTotal;
    }

    public static FenQiLeRepayTrialResponse fail(String msg) {
        FenQiLeRepayTrialResponse result = new FenQiLeRepayTrialResponse();
        result.setCode("1");
        result.setMsg(msg);
        return result;
    }
}
