package com.maguo.loan.cash.flow.service.listener;

import com.maguo.loan.cash.flow.config.RabbitConfig;
import com.maguo.loan.cash.flow.entity.UserRiskRecord;
import com.maguo.loan.cash.flow.entity.UserRiskRecordExternal;
import com.maguo.loan.cash.flow.enums.RiskChannel;
import com.maguo.loan.cash.flow.service.AbstractListener;
import com.maguo.loan.cash.flow.service.MqService;
import com.maguo.loan.cash.flow.service.RiskService;
import com.maguo.loan.cash.flow.service.WarningService;
import com.rabbitmq.client.Channel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;

/*
 * @Description: 风控审核结果通知监听
 * @Author: abai
 * @Date: 2025-8-18 下午 03:49
 */
@Component
public class RiskResultNoticeListener extends AbstractListener {

    private static final Logger logger = LoggerFactory.getLogger(RiskResultNoticeListener.class);

    private RiskService riskService;

    public RiskResultNoticeListener(MqService mqService, WarningService mqWarningService, RiskService riskService) {
        super(mqService, mqWarningService);
        this.riskService = riskService;
    }

    @RabbitListener(queues = RabbitConfig.Queues.RISK_RESULT_NOTICE_OUT)
    public void listenRiskResultNotice(Message message, Channel channel) {
        String riskId = new String(message.getBody(), StandardCharsets.UTF_8);

        try {
            logger.info("外部风控审核结果通知:{}", riskId);
            UserRiskRecordExternal record = riskService.findPlatformRiskRecordExternal(riskId);
            if (record == null || record.getUserId() == null) {
                getMqWarningService().warn("风控记录, riskId: " + riskId + ", 用户id 缺失");
                return;
            }
            if (RiskChannel.BW.name().equals(record.getRiskChannel().name())){
                riskService.baiWeiplatformRiskNotice(record);
            }

        } catch (Exception e) {
            processException(riskId, message, e, "百维风控审核结果通知异常", getMqService()::submitRiskResultNoticeOutDelay);
        } finally {
            ackMsg(riskId, message, channel);
        }
    }
}
