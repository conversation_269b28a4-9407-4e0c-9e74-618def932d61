package com.maguo.loan.cash.flow.entrance.fql.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;


/**
 * 分期乐配置
 */
@Configuration
public class FqlConfig {


    /**
     * 分期乐公钥(验签用)
     */
    @Value("${fql.client.lxSignPublicKey}")
    private  String lxSignPublicKey;

   /**
    * 合作方私钥(解密用)
    */
    @Value("${fql.servce.partnerPrivateKey}")
    private  String partnerPrivateKey;

    /**
     * 分期乐公钥(加密用)
     */
    @Value("${fql.client.lxPublicKey}")
    private  String lxPublicKey;

    /**
     * 合作方私钥(加签用)
     */
    @Value("${fql.servce.partnerSignPrivateKey}")
    private  String partnerSignPrivateKey;

    @Value("${fql.loan.sftpPath}")
    private String fqlLoanSftpPath;

    /**
     * 分期乐协议上传路径 add by yunhengtong 20250820
     */
    @Value("${fql.agreement.sftpPath}")
    private String fqlAgreementSftpPath;

    @Value("${fql.sftp.credit.apply.download}")
    private String fqlSftpCreditApplyDownloadPath;

    @Value("${fql.sftp.zzln.user}")
    private String sftpUser;
    @Value("${fql.sftp.zzln.password}")
    private String sftpPassword;
    @Value("${fql.sftp.zzln.host}")
    private String sftpHost;
    @Value("${fql.sftp.zzln.port}")
    private Integer sftpPort;

    /**
     * 是否跳过验签
     */
    @Value("${fql.skipSignVerify:false}")
    private Boolean skipSignVerify;

    /**
     * 百维公钥(验签用)
     */
    @Value("${baiwei.client.bwSignPublicKey}")
    private  String bwSignPublicKey;

    /**
     * 百维公钥(加密用)
     */
    @Value("${baiwei.client.bwPublicKey}")
    private  String bwPublicKey;

    /**
     * 合作方私钥(解密用)
     */
    @Value("${baiwei.servce.partnerPrivateKey}")
    private  String partnerPrivateKey2;

    /**
     * 合作方私钥(加签用)
     */
    @Value("${baiwei.servce.partnerSignPrivateKey}")
    private  String partnerSignPrivateKey2;

    public Boolean getSkipSignVerify() {
        return skipSignVerify;
    }

    public void setSkipSignVerify(Boolean skipSignVerify) {
        this.skipSignVerify = skipSignVerify;
    }

    public String getFqlSftpCreditApplyDownloadPath() {
        return fqlSftpCreditApplyDownloadPath;
    }
    public String getFqlLoanSftpPath() {
        return fqlLoanSftpPath;
    }


    public String getLxSignPublicKey() {
        return lxSignPublicKey;
    }

    public void setLxSignPublicKey(String lxSignPublicKey) {
        this.lxSignPublicKey = lxSignPublicKey;
    }

    public String getPartnerPrivateKey() {
        return partnerPrivateKey;
    }

    public void setPartnerPrivateKey(String partnerPrivateKey) {
        this.partnerPrivateKey = partnerPrivateKey;
    }

    public String getLxPublicKey() {
        return lxPublicKey;
    }

    public void setLxPublicKey(String lxPublicKey) {
        this.lxPublicKey = lxPublicKey;
    }

    public String getPartnerSignPrivateKey() {
        return partnerSignPrivateKey;
    }

    public void setPartnerSignPrivateKey(String partnerSignPrivateKey) {
        this.partnerSignPrivateKey = partnerSignPrivateKey;
    }

    public String getSftpUser() {
        return sftpUser;
    }

    public void setSftpUser(String sftpUser) {
        this.sftpUser = sftpUser;
    }

    public String getSftpPassword() {
        return sftpPassword;
    }

    public void setSftpPassword(String sftpPassword) {
        this.sftpPassword = sftpPassword;
    }

    public String getSftpHost() {
        return sftpHost;
    }

    public void setSftpHost(String sftpHost) {
        this.sftpHost = sftpHost;
    }

    public Integer getSftpPort() {
        return sftpPort;
    }

    public void setSftpPort(Integer sftpPort) {
        this.sftpPort = sftpPort;
    }

    public String getBwSignPublicKey() {
        return bwSignPublicKey;
    }

    public void setBwSignPublicKey(String bwSignPublicKey) {
        this.bwSignPublicKey = bwSignPublicKey;
    }

    public String getBwPublicKey() {
        return bwPublicKey;
    }

    public void setBwPublicKey(String bwPublicKey) {
        this.bwPublicKey = bwPublicKey;
    }

    public String getPartnerPrivateKey2() {
        return partnerPrivateKey2;
    }

    public void setPartnerPrivateKey2(String partnerPrivateKey2) {
        this.partnerPrivateKey2 = partnerPrivateKey2;
    }

    public String getPartnerSignPrivateKey2() {
        return partnerSignPrivateKey2;
    }

    public void setPartnerSignPrivateKey2(String partnerSignPrivateKey2) {
        this.partnerSignPrivateKey2 = partnerSignPrivateKey2;
    }

    /**
     * 分期乐协议上传路径拼接 add by yunhengtong 20250820
     */
    public String getAgreementSftpPath(String fileDateStr) {
        return fqlAgreementSftpPath + "/" + fileDateStr;
    }
}
