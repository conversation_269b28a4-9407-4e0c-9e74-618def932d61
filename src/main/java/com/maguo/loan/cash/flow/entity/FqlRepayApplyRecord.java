package com.maguo.loan.cash.flow.entity;

import jakarta.persistence.Entity;
import jakarta.persistence.Table;

import java.math.BigDecimal;

/**
 * 分期乐还款申请记录
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "fql_repay_apply_record")
public class FqlRepayApplyRecord extends BaseEntity {

    @Override
    protected String prefix() {
        return "FQL";
    }

    /**
     * 代扣请求流水号（分期乐侧支付流水号(为方便分期乐与支付通道对账，通过附加信息传输给支付通道)）
     */
    private String withholdSerialNo;
    /**
     * 合作方代码（资方定义，给乐信分配的代码）
     */
    private String partnerCode;
    /**
     * 资金方放款编号/借据号资金方订单唯一标识
     */
    private String loanId;
    /**
     * 用户绑卡信息
     */
    private String userBankCardId;
    /**
     * 通联需要的银行编码(交易侧透传给接入转换,可以参考中信)
     * 仅通联需要，按通联规范维护的银行编码(交易入参：bindCardInfo.standardBankCode)
     */
    private String bankId;
    /**
     * 代扣总金额=用户代扣金额+补差金额（单位：分）
     */
    private BigDecimal withholdAmt;
    /**
     * 签约协议号
     * 客户在支付机构的签约协议号
     * (通联以身份证号唯一，宝付以四要素唯一)
     * (交易入参：signNum)
     */
    private String signNum;
    /**
     * 支付模式
     * 0:银行卡支付
     * 1:余额支付
     * 2:份额支付
     * (交易入参：payMode)
     */
    private Integer payMode;
    /**
     * 分期乐在支付机构的商户号（交易入参:subMerchantId）
     */
    private String subMerchantId;
    /**
     * 加密报文（要加密的字段见后方文档）
     * 分期乐对出入账信息加密报文，需资方透传给支付机构，与资方申请一致方可扣款成功
     */
    private String encrpytContent;
    /**
     * 贷款申请编号
     */
    private String applyId;
    /**
     * 还款期数
     */
    private Integer repayTerm;
    /**
     * 还款类型
     */
    private Integer repayType;
    /**
     * 还款日期
     */
    private String repayDate;
    /**
     * 用户实际还款日
     */
    private String userRepayDate;
    /**
     * 还款路径:1-清分 2-代扣
     */
    private String repayChannel;
    /**
     * 其他参数
     */
    private String otherParamer;

    public String getWithholdSerialNo() {
        return withholdSerialNo;
    }

    public void setWithholdSerialNo(String withholdSerialNo) {
        this.withholdSerialNo = withholdSerialNo;
    }

    public String getPartnerCode() {
        return partnerCode;
    }

    public void setPartnerCode(String partnerCode) {
        this.partnerCode = partnerCode;
    }

    public String getLoanId() {
        return loanId;
    }

    public void setLoanId(String loanId) {
        this.loanId = loanId;
    }

    public String getUserBankCardId() {
        return userBankCardId;
    }

    public void setUserBankCardId(String userBankCardId) {
        this.userBankCardId = userBankCardId;
    }

    public String getBankId() {
        return bankId;
    }

    public void setBankId(String bankId) {
        this.bankId = bankId;
    }

    public BigDecimal getWithholdAmt() {
        return withholdAmt;
    }

    public void setWithholdAmt(BigDecimal withholdAmt) {
        this.withholdAmt = withholdAmt;
    }

    public String getSignNum() {
        return signNum;
    }

    public void setSignNum(String signNum) {
        this.signNum = signNum;
    }

    public Integer getPayMode() {
        return payMode;
    }

    public void setPayMode(Integer payMode) {
        this.payMode = payMode;
    }

    public String getSubMerchantId() {
        return subMerchantId;
    }

    public void setSubMerchantId(String subMerchantId) {
        this.subMerchantId = subMerchantId;
    }

    public String getEncrpytContent() {
        return encrpytContent;
    }

    public void setEncrpytContent(String encrpytContent) {
        this.encrpytContent = encrpytContent;
    }

    public String getApplyId() {
        return applyId;
    }

    public void setApplyId(String applyId) {
        this.applyId = applyId;
    }

    public Integer getRepayTerm() {
        return repayTerm;
    }

    public void setRepayTerm(Integer repayTerm) {
        this.repayTerm = repayTerm;
    }

    public Integer getRepayType() {
        return repayType;
    }

    public void setRepayType(Integer repayType) {
        this.repayType = repayType;
    }

    public String getRepayDate() {
        return repayDate;
    }

    public void setRepayDate(String repayDate) {
        this.repayDate = repayDate;
    }

    public String getUserRepayDate() {
        return userRepayDate;
    }

    public void setUserRepayDate(String userRepayDate) {
        this.userRepayDate = userRepayDate;
    }

    public String getRepayChannel() {
        return repayChannel;
    }

    public void setRepayChannel(String repayChannel) {
        this.repayChannel = repayChannel;
    }

    public String getOtherParamer() {
        return otherParamer;
    }

    public void setOtherParamer(String otherParamer) {
        this.otherParamer = otherParamer;
    }
}
