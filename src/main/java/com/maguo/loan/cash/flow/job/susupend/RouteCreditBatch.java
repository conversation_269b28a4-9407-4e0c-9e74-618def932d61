package com.maguo.loan.cash.flow.job.susupend;


import com.jinghang.capital.api.dto.BankChannel;
import com.jinghang.cash.api.dto.ProjectElementsDto;
import com.jinghang.cash.api.dto.ProjectInfoDto;
import com.jinghang.cash.api.enums.CapitalRoute;
import com.jinghang.common.util.CollectionUtil;
import com.jinghang.common.util.JsonUtil;
import com.maguo.loan.cash.flow.common.BizException;
import com.maguo.loan.cash.flow.common.ResultCode;
import com.maguo.loan.cash.flow.dto.ProcessDTO;
import com.maguo.loan.cash.flow.entity.Order;
import com.maguo.loan.cash.flow.entrance.common.service.ProjectInfoService;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import com.maguo.loan.cash.flow.enums.OrderState;
import com.maguo.loan.cash.flow.job.AbstractJobHandler;
import com.maguo.loan.cash.flow.job.JobParam;
import com.maguo.loan.cash.flow.repository.CapitalConfigRepository;
import com.maguo.loan.cash.flow.repository.OrderRepository;
import com.maguo.loan.cash.flow.service.OrderService;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/10/10
 * 资金路由
 */
@Component
@JobHandler("routeCredit")
public class RouteCreditBatch extends AbstractJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(RouteCreditBatch.class);

    @Autowired
    private OrderService orderService;
    @Autowired
    private OrderRepository orderRepository;
    @Autowired
    private CapitalConfigRepository capitalConfigRepository;
    @Autowired
    private ProjectInfoService projectInfoService;
    @Override
    public void doJob(JobParam jobParam) {
        logger.info("routeCredit jobParam:{}", JsonUtil.toJsonString(jobParam));
        if (jobParam != null && jobParam.getOrderId() != null) {
            String orderId = jobParam.getOrderId();
            Order order = orderRepository.findById(orderId).orElseThrow(() -> new BizException(ResultCode.ORDER_NOT_EXIST));
            if (StringUtils.isEmpty(order.getProjectCode())){
                //项目编码为空，依旧走路由
                orderService.orderRoute(order);
            }else {
                ProjectInfoDto projectInfoVO = projectInfoService.queryProjectInfo(order.getProjectCode());
                ProjectElementsDto elements = projectInfoVO.getElements();//项目要素

                if (CapitalRoute.ROUTE == elements.getCapitalRoute()){
                    orderService.orderRoute(order);
                    return;
                }
                if (CapitalRoute.DIRECT == elements.getCapitalRoute()){
                    orderService.orderDirect(order);
                    return;
                }
                logger.warn("routeCredit挂起订单激活失败, 没有对应的路由规则，orderId: [{}]", orderId);
            }
        } else if (jobParam != null && jobParam.getFlowChannel() != null) {
            FlowChannel flowChannel = jobParam.getFlowChannel();
            List<Order> orders = orderRepository.findByOrderStateAndFlowChannel(OrderState.SUSPENDED, flowChannel);
            if (CollectionUtil.isEmpty(orders)) {
                logger.warn("routeCredit被挂起的订单数量为0, flowChannel: {}", flowChannel);
                return;
            }
            logger.warn("routeCredit 被挂起的订单数量为{},开始处理, flowChannel: {}", orders.size(), flowChannel);
            for (Order order : orders) {
                if (StringUtils.isEmpty(order.getProjectCode())){
                    //项目编码为空，依旧走路由
                    orderService.orderRoute(order);
                }else {
                    ProjectInfoDto projectInfoVO = projectInfoService.queryProjectInfo(order.getProjectCode());
                    ProjectElementsDto elements = projectInfoVO.getElements();//项目要素

                    if (CapitalRoute.ROUTE == elements.getCapitalRoute()){
                        orderService.orderRoute(order);
                        continue;
                    }
                    if (CapitalRoute.DIRECT == elements.getCapitalRoute()){
                        orderService.orderDirect(order);
                        continue;
                    }
                    logger.warn("routeCredit挂起订单激活失败, 没有对应的路由规则，orderId: [{}]", order.getId());
                }
            }
        } else {
            //所有被挂起的订单
            List<String> suspendedOrderIds = new ArrayList<>();
            //查询被挂起的订单
            String time =  LocalDateTime.now().format(DateTimeFormatter.ofPattern("HH:mm:ss"));

            //获取路由挂起订单
            //获取到当期任务时间，去定位到需要激活的渠道流量，资方
            List<ProcessDTO> list = capitalConfigRepository.findByTime(time).stream()
                .map(map -> new ProcessDTO(
                    (String) map.get("flowChannel"),
                    (String) map.get("bankChannel")
                ))
                .collect(Collectors.toList());
            if(CollectionUtil.isNotEmpty(list)){
                for(ProcessDTO  dto:list) {
                    FlowChannel flowChannel= FlowChannel.valueOf(dto.getFlowChannel());
                    BankChannel bankChannel= BankChannel.valueOf(dto.getBankChannel());
                    List<String> orderIds = orderRepository.findIdsByOrderState(OrderState.SUSPENDED,flowChannel, bankChannel);
                    logger.info("routeCredit 路由订单挂起数量为{}", orderIds.size());
                    //将挂起订单添加到所有挂起订单集合中
                    suspendedOrderIds.addAll(orderIds);
                }
            }

            //获取直连挂起订单
            List<ProjectInfoDto> projectInfoVOS = projectInfoService.queryAll();
            for (ProjectInfoDto projectInfoVO : projectInfoVOS) {
                ProjectElementsDto elements = projectInfoVO.getElements();
                String loanDarkHours = elements.getLoanDarkHours();//例如：21:50-06:00
                String[] split = loanDarkHours.split("-");
                if (split.length != 2){
                    logger.warn("项目用信黑暗期取值错误：[{}]，项目唯一编码：[{}]",loanDarkHours,projectInfoVO.getProjectCode());
                    continue;
                }
                LocalTime startTime = LocalTime.parse(split[0], DateTimeFormatter.ofPattern("HH:mm"));
                LocalTime endTime = LocalTime.parse(split[1], DateTimeFormatter.ofPattern("HH:mm"));
                LocalTime nowTime = LocalTime.parse(time, DateTimeFormatter.ofPattern("HH:mm:ss"));
                //此判断只能判断黑暗期跨夜的情况
                if (nowTime.isBefore(startTime) && nowTime.isAfter(endTime)){
                    //当前时间在21:50之前，且在06:00之后
                    List<String> orderIds = orderRepository.findIdsByOrderStateAndProjectCode(OrderState.SUSPENDED,projectInfoVO.getProjectCode());
                    logger.info("routeCredit 直连订单挂起数量为{}", orderIds.size());
                    //将挂起订单添加到所有挂起订单集合中
                    suspendedOrderIds.addAll(orderIds);
                }
            }

            //所有被挂起的订单去重
            List<String> uniqueList = suspendedOrderIds.stream()
                .distinct()
                .collect(Collectors.toList());

            //处理挂起订单激活
            for (String orderId : uniqueList) {
                try {
                    Order order = orderRepository.findById(orderId).orElseThrow();
                    if (OrderState.SUSPENDED != order.getOrderState()) {
                        logger.info("routeCredit 订单[{}]状态为[{}],不激活挂起", orderId, order.getOrderState());
                        continue;
                    }

                    if (StringUtils.isEmpty(order.getProjectCode())){
                        //项目编码为空，依旧走路由
                        orderService.orderRoute(order);
                    }else {
                        ProjectInfoDto projectInfoVO = projectInfoService.queryProjectInfo(order.getProjectCode());
                        ProjectElementsDto elements = projectInfoVO.getElements();//项目要素
                        //todo 路由规则枚举待定
                        if (CapitalRoute.ROUTE == elements.getCapitalRoute()){
                            orderService.orderRoute(order);
                            continue;
                        }
                        if (CapitalRoute.DIRECT == elements.getCapitalRoute()){
                            orderService.orderDirect(order);
                            continue;
                        }
                        logger.warn("routeCredit挂起订单激活失败, 没有对应的路由规则，orderId: [{}]", order.getId());
                    }

                } catch (Exception e) {
                    logger.error("routeCredit 订单[{}]挂起激活异常:", orderId, e);
                }
            }
        }
        logger.info("routeCredit end");
    }

}
