package com.maguo.loan.cash.flow.entrance.fql.dto.callBack;

/**
 * @Description 查询资方放款申请风控审核结果 响应参数
 * @Date 2025/08/18 09:51
 * @Version v1.0
 **/
public class BaiWeiInvestorsAuditResultQueryResponse {

    /**
     * 长银借据号
     */
    private String loanNo;
    /**
     * 放款结果
     */
    private Integer loanStatus;
    /**
     * 放款结果描述
     */
    private String msg;

    public String getLoanNo() {
        return loanNo;
    }

    public void setLoanNo(String loanNo) {
        this.loanNo = loanNo;
    }

    public Integer getLoanStatus() {
        return loanStatus;
    }

    public void setLoanStatus(Integer loanStatus) {
        this.loanStatus = loanStatus;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public static BaiWeiInvestorsAuditResultQueryResponse fail(String msg) {
        BaiWeiInvestorsAuditResultQueryResponse result = new BaiWeiInvestorsAuditResultQueryResponse();
        result.setLoanStatus(3);
        result.setMsg(msg);
        return result;
    }

    public static BaiWeiInvestorsAuditResultQueryResponse returnResult(String loanNo, Integer loanStatus, String msg) {
        BaiWeiInvestorsAuditResultQueryResponse result = new BaiWeiInvestorsAuditResultQueryResponse();
        result.setLoanNo(loanNo);
        result.setLoanStatus(loanStatus);
        result.setMsg(msg);
        return result;
    }
}
