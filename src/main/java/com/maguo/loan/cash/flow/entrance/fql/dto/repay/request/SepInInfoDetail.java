package com.maguo.loan.cash.flow.entrance.fql.dto.repay.request;

import java.math.BigDecimal;

/**
 * 分账明细(可能有多条明细)
 */
public class SepInInfoDetail {
    /**
     * 对应金额
     */
    private BigDecimal amt;
    /**
     * 表示从用户和补差账户的出账金额,对应sepOutInfo中的type
     */
    private String from;

    public BigDecimal getAmt() {
        return amt;
    }

    public void setAmt(BigDecimal amt) {
        this.amt = amt;
    }

    public String getFrom() {
        return from;
    }

    public void setFrom(String from) {
        this.from = from;
    }
}
