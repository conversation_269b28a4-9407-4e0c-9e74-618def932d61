package com.maguo.loan.cash.flow.config;

import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

/**
 * Redisson 配置类
 * 解决 Redis WAIT 命令不支持的问题
 *
 * 问题原因：Apollo 配置使用了 clusterServersConfig 但实际是单机 Redis
 * 解决方案：强制使用单机配置，避免集群相关的 WAIT 命令
 */
@Configuration
@ConditionalOnProperty(name = "spring.redis.redisson.fix-wait-command", havingValue = "true", matchIfMissing = true)
public class RedissonConfig {

    @Bean
    @Primary
    public RedissonClient redissonClient() {
        Config config = new Config();

        // 使用阿里云 Redis 单机配置
        String address = "redis://r-uf6wivfgg7d5jnp0vgpd.redis.rds.aliyuncs.com:6379";
        String password = "hEztzGoBv6k#fZ!f";

        // 单机模式配置 - 避免集群模式的 WAIT 命令
        config.useSingleServer()
                .setAddress(address)
                .setPassword(password)
                .setDatabase(0)
                .setConnectionMinimumIdleSize(8)
                .setConnectionPoolSize(24)
                .setSubscriptionConnectionMinimumIdleSize(1)
                .setSubscriptionConnectionPoolSize(10)
                .setIdleConnectionTimeout(10000)
                .setConnectTimeout(10000)
                .setTimeout(3000)
                .setRetryAttempts(3)
                .setRetryInterval(1500)
                .setPingConnectionInterval(30000)
                .setKeepAlive(true)
                .setTcpNoDelay(true);

        // 设置线程数
        config.setThreads(16);
        config.setNettyThreads(32);

        return Redisson.create(config);
    }
}
