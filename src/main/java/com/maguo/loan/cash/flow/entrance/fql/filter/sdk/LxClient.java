package com.maguo.loan.cash.flow.entrance.fql.filter.sdk;

import com.maguo.loan.cash.flow.entrance.fql.filter.sdk.utils.RsaEncryptManager;
import com.maguo.loan.cash.flow.entrance.fql.filter.sdk.utils.RsaSignManager;


public class LxClient {
//    private String privateKey;
//    private String publicKey;

//    public LxClient(String privateKey,  String publicKey) {
////        this.encryptionMode = EncryptionModeEnum.RSA1024AES;
////        this.signType = SignTypeEnum.HMACSHA1;
////        this.bizParam = "data";
////        this.charset = "UTF-8";
////        this.connectTimeout = 6000;
////        this.readTimeout = 60000;
////        this.uploadTimeout = 7200000;
////        this.gatewayUrl = gatewayUrl;
////        this.channelId = channelId;
//        this.privateKey = privateKey;
//        this.publicKey = publicKey;
//    }

    public String encrypt(String request,String publicKey){
        RsaEncryptManager rsaEncryptManager = new RsaEncryptManager();
        return rsaEncryptManager.encrypyt(request,publicKey);

    }

    public String decrypt(String string,String privateKey) {
        RsaEncryptManager rsaEncryptManager = new RsaEncryptManager();
        return rsaEncryptManager.decryption(string,privateKey);

    }

    public String sign(String data,String privateKey){
        RsaSignManager rsaSignManager = new RsaSignManager();
        return rsaSignManager.sign(data,privateKey);

    }

    public boolean verifySign(String data,String sign,String publicKey) {
        RsaSignManager rsaSignManager = new RsaSignManager();
        return rsaSignManager.verifySign(data,publicKey,sign);

    }

}
