package com.maguo.loan.cash.flow.entrance.fql.dto.repay;

import com.maguo.loan.cash.flow.entrance.fql.dto.repay.response.WithholdDetailResponse;

import java.util.List;

/**
 * @Description 代扣结果查询 响应参数
 * @Date 2025/08/08 16:51
 * @Version v1.0
 **/
public class FenQiLeRepayResultResponse {

    /**
     * 代扣状态
     * 1、代扣成功
     * 2、代扣失败
     * 3. 代扣处理中
     * 4. 未收到代扣请求（交易侧可以重复发起代扣）"
     */
    private Integer status;
    /**
     * 代扣描述
     */
    private String msg;
    /**
     * 资金方扣款交易流水号
     */
    private String orderNum;
    /**
     * 支付通道交易流水号
     */
    private String transNum;
    /**
     * 代扣明细
     */
    private List<WithholdDetailResponse> withholdDetail;

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public String getOrderNum() {
        return orderNum;
    }

    public void setOrderNum(String orderNum) {
        this.orderNum = orderNum;
    }

    public String getTransNum() {
        return transNum;
    }

    public void setTransNum(String transNum) {
        this.transNum = transNum;
    }

    public List<WithholdDetailResponse> getWithholdDetail() {
        return withholdDetail;
    }

    public void setWithholdDetail(List<WithholdDetailResponse> withholdDetail) {
        this.withholdDetail = withholdDetail;
    }

    public static FenQiLeRepayResultResponse fail(String msg) {
        FenQiLeRepayResultResponse result = new FenQiLeRepayResultResponse();
        result.setStatus(2);
        result.setMsg(msg);
        return result;
    }
}
