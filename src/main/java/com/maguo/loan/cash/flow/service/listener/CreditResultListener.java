package com.maguo.loan.cash.flow.service.listener;

import com.maguo.loan.cash.flow.config.RabbitConfig;
import com.maguo.loan.cash.flow.service.AbstractListener;
import com.maguo.loan.cash.flow.service.MqService;
import com.maguo.loan.cash.flow.service.OrderRouterService;
import com.maguo.loan.cash.flow.service.OrderService;
import com.maguo.loan.cash.flow.service.WarningService;
import com.rabbitmq.client.Channel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;

/*
 * @Description: 直连资方授信处理后续
 * @Author: abai
 * @Date: 2025-8-21 下午 04:25
 */
@Component
public class CreditResultListener extends AbstractListener {

    private static final Logger logger = LoggerFactory.getLogger(CreditResultListener.class);

    private OrderService orderService;

    public CreditResultListener(MqService mqService, WarningService mqWarningService) {
        super(mqService, mqWarningService);
    }

    @RabbitListener(queues = RabbitConfig.Queues.CREDIT_RESULT)
    public void routeResultListen(Message message, Channel channel) {
        String creditId = new String(message.getBody(), StandardCharsets.UTF_8);
        try {
            logger.info("直连资方授信结果:{}", creditId);
            // service
            orderService.loopBack(creditId);
        } catch (Exception e) {
            getMqWarningService().warn("直连资方授信结果异常:" + e.getMessage(), msg -> logger.error(msg, e));
        } finally {
            ackMsg(creditId, message, channel);
        }
    }

    @Autowired
    public void setOrderService(OrderService orderService) {
        this.orderService = orderService;
    }
}
