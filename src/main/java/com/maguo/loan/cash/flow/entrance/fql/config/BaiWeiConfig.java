package com.maguo.loan.cash.flow.entrance.fql.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * @Description: 百维风控配置
 * @Author: abai
 * @Date: 2025-8-15 下午 03:49
 */
@Configuration
public class BaiWeiConfig {
    /**
     *  百维sftp账户
     */
    @Value("${baiwei.sftp.zzln.user}")
    private String sftpUser;
    /**
     *  百维sftp密码
     */
    @Value("${baiwei.sftp.zzln.password}")
    private String sftpPassword;
    /**
     *  百维sftp-host
     */
    @Value("${baiwei.sftp.zzln.host}")
    private String sftpHost;
    /**
     *  百维sftp端口
     */
    @Value("${baiwei.sftp.zzln.port}")
    private Integer sftpPort;
    /**
     * 百维签章sftpPath
     * /download/合作方代码/contract/#biz_date@yyyyMMdd#/
     */
    @Value("${baiwei.agreement.sftpPath}")
    private String agreementSftpPath;
    /**
     * 百维影像sftpPath
     * /upload/合作方代码/image/yyyyMMdd
     */
    @Value("${baiwei.image.sftpPath}")
    private String imageSftpPath;

    public String getSftpUser() {
        return sftpUser;
    }

    public String getSftpPassword() {
        return sftpPassword;
    }

    public String getSftpHost() {
        return sftpHost;
    }

    public Integer getSftpPort() {
        return sftpPort;
    }

    public String getAgreementSftpPath(String bizDate, String fileDate) {
        return String.format(agreementSftpPath, bizDate, fileDate);
    }

    public String getImageSftpPath(String fileDate) {
        return String.format(imageSftpPath, fileDate);
    }
}
