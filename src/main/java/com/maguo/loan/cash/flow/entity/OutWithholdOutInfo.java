package com.maguo.loan.cash.flow.entity;

import jakarta.persistence.Entity;
import jakarta.persistence.Table;

import java.math.BigDecimal;

/**
 * <p>
 * 外部代扣出账信息
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-07
 */
@Entity
@Table(name = "out_withhold_out_info")
public class OutWithholdOutInfo extends BaseEntity  {

    /**
     * 用户账户出账金额（单位 分
     */
    private BigDecimal userAmount;

    /**
     * 补差账户出账金额（单位 分
     */
    private BigDecimal diffAmount;

    /**
     * 用户账户
     */
    private String userAccount;

    /**
     * 补差账户
     */
    private String diffAccount;

    /**
     * 外部代扣id
     */
    private String outWithholdFlowId;

    public BigDecimal getUserAmount() {
        return userAmount;
    }

    public void setUserAmount(BigDecimal userAmount) {
        this.userAmount = userAmount;
    }

    public BigDecimal getDiffAmount() {
        return diffAmount;
    }

    public void setDiffAmount(BigDecimal diffAmount) {
        this.diffAmount = diffAmount;
    }

    public String getUserAccount() {
        return userAccount;
    }

    public void setUserAccount(String userAccount) {
        this.userAccount = userAccount;
    }

    public String getDiffAccount() {
        return diffAccount;
    }

    public void setDiffAccount(String diffAccount) {
        this.diffAccount = diffAccount;
    }

    public String getOutWithholdFlowId() {
        return outWithholdFlowId;
    }

    public void setOutWithholdFlowId(String outWithholdFlowId) {
        this.outWithholdFlowId = outWithholdFlowId;
    }

    @Override
    protected String prefix() {
        return "OI";
    }
}
