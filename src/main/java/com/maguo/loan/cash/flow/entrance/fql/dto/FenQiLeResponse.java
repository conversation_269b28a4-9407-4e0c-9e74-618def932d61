package com.maguo.loan.cash.flow.entrance.fql.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.jinghang.common.util.JsonUtil;
import com.maguo.loan.cash.flow.entrance.fql.exception.FenQiLeResultCode;

/**
 * @ClassName Response
 * <AUTHOR>
 * @Description 分期乐 加密后返回参数
 * @Date 2025/8/6 15:36
 * @Version v1.0
 **/
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class FenQiLeResponse extends FenQiLeEncryptData {

    private String code;

    private String msg;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public static FenQiLeResponse success() {
        FenQiLeResponse result = new FenQiLeResponse();
        result.setCode(FenQiLeResultCode.SUCCESS.getCode());
        result.setMsg(FenQiLeResultCode.SUCCESS.getMsg());
        return result;
    }

    public static FenQiLeResponse success(Object data) {
        FenQiLeResponse result = success();
        result.setBizContent(JsonUtil.toJsonString(data));
        return result;
    }

    public static FenQiLeResponse fail() {
        FenQiLeResponse result = new FenQiLeResponse();
        result.setCode(FenQiLeResultCode.SYSTEM_ERROR.getCode());
        result.setMsg(FenQiLeResultCode.SYSTEM_ERROR.getMsg());
        return result;
    }

    public static FenQiLeResponse fail(String message) {
        FenQiLeResponse result = new FenQiLeResponse();
        result.setCode(FenQiLeResultCode.SYSTEM_ERROR.getCode());
        result.setMsg(message);
        return result;
    }

    public static FenQiLeResponse fail(String status, String message) {
        FenQiLeResponse result = new FenQiLeResponse();
        result.setCode(status);
        result.setMsg(message);
        return result;
    }

    public static FenQiLeResponse exception(String msg) {
        FenQiLeResponse result = new FenQiLeResponse();
        result.setCode(FenQiLeResultCode.SYSTEM_ERROR.getCode());
        result.setMsg(msg);
        return result;
    }
}
