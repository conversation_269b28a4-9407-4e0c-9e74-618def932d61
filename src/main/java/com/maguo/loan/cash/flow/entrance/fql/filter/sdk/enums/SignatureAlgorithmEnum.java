package com.maguo.loan.cash.flow.entrance.fql.filter.sdk.enums;



/**
 * @Author: tony<PERSON>
 * @Date: 2020/2/17 18:04
 * @Description:  加签的签名类型
 *
 * 附件类型：
signatureInfo.put("sun.security.provider.DSA$SHA1withDSA", TRUE);
signatureInfo.put("sun.security.rsa.RSASignature$MD2withRSA", TRUE);
signatureInfo.put("sun.security.rsa.RSASignature$MD5withRSA", TRUE);
signatureInfo.put("sun.security.rsa.RSASignature$SHA1withRSA", TRUE);
signatureInfo.put("sun.security.rsa.RSASignature$SHA256withRSA", TRUE);
signatureInfo.put("sun.security.rsa.RSASignature$SHA384withRSA", TRUE);
signatureInfo.put("sun.security.rsa.RSASignature$SHA512withRSA", TRUE);
 *
 */
public enum SignatureAlgorithmEnum {


    SHA256WITHRSA( "SHA256withRSA"),
    SHA512withRSA( "SHA512withRSA");


    private String key;

    SignatureAlgorithmEnum(String key) {
        this.key = key;
    }


    public String getKey() {
        return key;
    }




}
