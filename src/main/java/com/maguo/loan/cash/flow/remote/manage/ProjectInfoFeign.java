package com.maguo.loan.cash.flow.remote.manage;

import com.jinghang.cash.api.ProjectInfoApiService;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * 项目信息管理服务Feign客户端
 * 用于Flow模块调用Cash-Manage模块的项目信息管理接口
 *
 * @Author: Lior
 * @CreateTime: 2025/8/21 15:49
 */
@FeignClient(name = "cash-manage", contextId = "projectInfo", path = "/api/project/info")
public interface ProjectInfoFeign extends ProjectInfoApiService {
}
