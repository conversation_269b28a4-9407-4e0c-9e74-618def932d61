package com.maguo.loan.cash.flow.entrance.fql.dto.repay.request;

import java.math.BigDecimal;
import java.util.List;

/**
 * 分账信息
 * [{
 * "withholdMerchants": "是否为代扣商户号，1-是， 0-否",
 * "orgType": "机构类型 ：1：主扣款商户号， 2：被分账商户号',
 * "amt": "分账总金额 ,（单位 分）",
 * "detail（分账明细(可能有多条明细))": [{
 * "amt": "对应金额",
 * "from": "表示从用户和补差账户的出账金额,对应sepOutInfo中的type"
 * }],
 * "sepMerchCode": "机构编码",
 * "type": "type:被分账对象 （收款主体类型） 1：资方 2：担保方/其他",
 * "sepBankId": "账号所属银行",
 * "account": "入账商户号"
 * }]
 * <p>
 * 备注：分账信息条数大于等于一条，当担保费为0时，担保费分账信息不会透传给资方，产品需提示资方做好适配
 */
public class SepInInfo {
    /**
     * 是否为代扣商户号，1-是， 0-否
     */
    private Integer withholdMerchants;
    /**
     * 机构类型 ：1：主扣款商户号， 2：被分账商户号
     */
    private Integer orgType;
    /**
     * 分账总金额 ,（单位 分）
     */
    private BigDecimal amt;
    /**
     * （分账明细(可能有多条明细))
     */
    private List<SepInInfoDetail> detail;
    /**
     * 机构编码
     */
    private String sepMerchCode;
    /**
     * type:被分账对象 （收款主体类型） 1：资方 2：担保方/其他
     */
    private Integer type;
    /**
     * 账号所属银行
     */
    private String sepBankId;
    /**
     * 入账商户号
     */
    private String account;

    public Integer getWithholdMerchants() {
        return withholdMerchants;
    }

    public void setWithholdMerchants(Integer withholdMerchants) {
        this.withholdMerchants = withholdMerchants;
    }

    public Integer getOrgType() {
        return orgType;
    }

    public void setOrgType(Integer orgType) {
        this.orgType = orgType;
    }

    public BigDecimal getAmt() {
        return amt;
    }

    public void setAmt(BigDecimal amt) {
        this.amt = amt;
    }

    public List<SepInInfoDetail> getDetail() {
        return detail;
    }

    public void setDetail(List<SepInInfoDetail> detail) {
        this.detail = detail;
    }

    public String getSepMerchCode() {
        return sepMerchCode;
    }

    public void setSepMerchCode(String sepMerchCode) {
        this.sepMerchCode = sepMerchCode;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getSepBankId() {
        return sepBankId;
    }

    public void setSepBankId(String sepBankId) {
        this.sepBankId = sepBankId;
    }

    public String getAccount() {
        return account;
    }

    public void setAccount(String account) {
        this.account = account;
    }
}
