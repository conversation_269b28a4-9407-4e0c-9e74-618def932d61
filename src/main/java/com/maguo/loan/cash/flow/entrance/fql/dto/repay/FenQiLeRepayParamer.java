package com.maguo.loan.cash.flow.entrance.fql.dto.repay;

import com.maguo.loan.cash.flow.entrance.fql.dto.repay.request.BindCardInfo;

import java.math.BigDecimal;

/**
 * 分期乐还款数据
 */
public class FenQiLeRepayParamer {

    /**
     * 放款id
     */
    private String loanGid;
    /**
     * 还款订单号
     */
    private String repaymentGid;
    /**
     * 还款期数
     */
    private Integer period;
    //	还款类型	(1-正常还款、 3-逾期还款):还当期; 4-全部提前还:结清  其他不支持
    private String repayType;
    //还款渠道	ONLINE线上 OFFLINE线下
    private String repayMode;
    //客户实际还款日期
    private String repayDate;
    //客户扣款总金额
    private BigDecimal repayTotalAmount;
    /**
     * 用户绑卡信息
     */
    private BindCardInfo bindCardInfo;

    public String getLoanGid() {
        return loanGid;
    }

    public void setLoanGid(String loanGid) {
        this.loanGid = loanGid;
    }

    public String getRepaymentGid() {
        return repaymentGid;
    }

    public void setRepaymentGid(String repaymentGid) {
        this.repaymentGid = repaymentGid;
    }

    public Integer getPeriod() {
        return period;
    }

    public void setPeriod(Integer period) {
        this.period = period;
    }

    public String getRepayType() {
        return repayType;
    }

    public void setRepayType(String repayType) {
        this.repayType = repayType;
    }

    public String getRepayMode() {
        return repayMode;
    }

    public void setRepayMode(String repayMode) {
        this.repayMode = repayMode;
    }

    public String getRepayDate() {
        return repayDate;
    }

    public void setRepayDate(String repayDate) {
        this.repayDate = repayDate;
    }

    public BigDecimal getRepayTotalAmount() {
        return repayTotalAmount;
    }

    public void setRepayTotalAmount(BigDecimal repayTotalAmount) {
        this.repayTotalAmount = repayTotalAmount;
    }

    public BindCardInfo getBindCardInfo() {
        return bindCardInfo;
    }

    public void setBindCardInfo(BindCardInfo bindCardInfo) {
        this.bindCardInfo = bindCardInfo;
    }
}
