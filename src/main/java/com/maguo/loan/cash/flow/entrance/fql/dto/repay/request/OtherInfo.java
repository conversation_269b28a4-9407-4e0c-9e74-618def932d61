package com.maguo.loan.cash.flow.entrance.fql.dto.repay.request;

import java.math.BigDecimal;

/**
 * 可能会其他类型的金额,用json数组的格式提高扩展性
 * 其他科目金额
 * [{
 * "fee": "费用（单位：分）",
 * "type": "（费用大项）1:保费, 2:信用评估费类",
 * "subType": "（费用子项） 57:担保咨询服务费（属于信用评估费类型）, 50:信用评估费（属于信用评估费类型）",40保费
 * "insureMode": "分期乐内部担保模式号"
 * }]
 * 确认资金方在业务上是否需要,不需要就不给这个字段
 */
public class OtherInfo {
    /**
     * 费项金额
     * 费用（单位：分）
     */
    private BigDecimal fee;
    /**
     * 信用评估费类
     * （费用大项）1:保费, 2:信用评估费类
     */
    private Integer type;
    /**
     * （费用子项） 57:担保咨询服务费（属于信用评估费类型）, 50:信用评估费（属于信用评估费类型）",40保费
     */
    private Integer subType;
    /**
     * 担保模式
     * 分期乐内部担保模式号
     */
    private String insureMode;

    public BigDecimal getFee() {
        return fee;
    }

    public void setFee(BigDecimal fee) {
        this.fee = fee;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getSubType() {
        return subType;
    }

    public void setSubType(Integer subType) {
        this.subType = subType;
    }

    public String getInsureMode() {
        return insureMode;
    }

    public void setInsureMode(String insureMode) {
        this.insureMode = insureMode;
    }
}
