package com.maguo.loan.cash.flow.entrance.fql.dto.repay.request;

/**
 * 用户绑卡信息
 * 包含用户四要素：手机号、卡号、用户名、身份证等
 * {
 * "uid":"分期乐uid",
 * "userName":"用户姓名",
 * "cardNo":"银行卡号",
 * "bankType":"分期乐开户银行简码",
 * "bankBin":"开户银行卡bin",
 * "bankFullName":"开户银行全称",
 * "idType":"身份证类型,100:身份证号码 110:港澳通行证 120:护照",
 * "idNo":"身份证",
 * "phoneNo":"银行卡预留手机号",
 * "identityId":"通联/宝付绑卡协议号",
 * "standardBankType":"",
 * "standardBankCode":"通联侧银行数字编码"
 * }
 */
public class BindCardInfo {
    /**
     * 分期乐uid
     */
    private String uid;
    /**
     * 用户姓名
     */
    private String userName;
    /**
     * 银行卡号
     */
    private String cardNo;
    /**
     * 分期乐开户银行简码
     */
    private String bankType;
    /**
     * 开户银行卡bin
     */
    private String bankBin;
    /**
     * 开户银行全称
     */
    private String bankFullName;
    /**
     * 身份证类型,100:身份证号码 110:港澳通行证 120:护照
     */
    private String idType;
    /**
     * 身份证
     */
    private String idNo;
    /**
     * 银行卡预留手机号
     */
    private String phoneNo;
    /**
     * 通联/宝付绑卡协议号
     */
    private String identityId;
    /**
     * 通联侧银行数字类型
     */
    private String standardBankType;
    /**
     * 通联侧银行数字编码
     */
    private String standardBankCode;

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getCardNo() {
        return cardNo;
    }

    public void setCardNo(String cardNo) {
        this.cardNo = cardNo;
    }

    public String getBankType() {
        return bankType;
    }

    public void setBankType(String bankType) {
        this.bankType = bankType;
    }

    public String getBankBin() {
        return bankBin;
    }

    public void setBankBin(String bankBin) {
        this.bankBin = bankBin;
    }

    public String getBankFullName() {
        return bankFullName;
    }

    public void setBankFullName(String bankFullName) {
        this.bankFullName = bankFullName;
    }

    public String getIdType() {
        return idType;
    }

    public void setIdType(String idType) {
        this.idType = idType;
    }

    public String getIdNo() {
        return idNo;
    }

    public void setIdNo(String idNo) {
        this.idNo = idNo;
    }

    public String getPhoneNo() {
        return phoneNo;
    }

    public void setPhoneNo(String phoneNo) {
        this.phoneNo = phoneNo;
    }

    public String getIdentityId() {
        return identityId;
    }

    public void setIdentityId(String identityId) {
        this.identityId = identityId;
    }

    public String getStandardBankType() {
        return standardBankType;
    }

    public void setStandardBankType(String standardBankType) {
        this.standardBankType = standardBankType;
    }

    public String getStandardBankCode() {
        return standardBankCode;
    }

    public void setStandardBankCode(String standardBankCode) {
        this.standardBankCode = standardBankCode;
    }
}
