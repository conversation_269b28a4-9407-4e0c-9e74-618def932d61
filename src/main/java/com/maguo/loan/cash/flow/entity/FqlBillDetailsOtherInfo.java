package com.maguo.loan.cash.flow.entity;

import jakarta.persistence.Entity;
import jakarta.persistence.Table;

import java.math.BigDecimal;

/**
 * <p>
 * 可能会其他类型的金额,用json数组的格式提高扩展性其他科目金额
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-13
 */

@Entity
@Table(name = "fql_bill_details_other_info")
public class FqlBillDetailsOtherInfo extends BaseEntity {

    /**
     * 费项金额（单位：分）
     */
    private BigDecimal fee;

    /**
     * 费用大项）1:保费, 2:信用评估费类
     */
    private Integer type;

    /**
     * 费用子项） 57:担保咨询服务费（属于信用评估费类型）, 50:信用评估费（属于信用评估费类型）",40保费
     */
    private Integer subType;

    /**
     * 分期乐内部担保模式号
     */
    private String insureMode;

    /**
     * 分期乐还款账单明细id
     */
    private String fqlBillDetailsId;

    public BigDecimal getFee() {
        return fee;
    }

    public void setFee(BigDecimal fee) {
        this.fee = fee;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getSubType() {
        return subType;
    }

    public void setSubType(Integer subType) {
        this.subType = subType;
    }

    public String getInsureMode() {
        return insureMode;
    }

    public void setInsureMode(String insureMode) {
        this.insureMode = insureMode;
    }

    public String getFqlBillDetailsId() {
        return fqlBillDetailsId;
    }

    public void setFqlBillDetailsId(String fqlBillDetailsId) {
        this.fqlBillDetailsId = fqlBillDetailsId;
    }

    @Override
    protected String prefix() {
        return "DOI";
    }
}
