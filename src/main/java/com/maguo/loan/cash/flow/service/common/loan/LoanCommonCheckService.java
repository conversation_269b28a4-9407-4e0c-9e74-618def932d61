package com.maguo.loan.cash.flow.service.common.loan;

import com.jinghang.cash.api.dto.ProjectInfoDto;
import com.jinghang.cash.api.enums.RepaymentType;
import com.maguo.loan.cash.flow.common.BizException;
import com.maguo.loan.cash.flow.common.ResultCode;
import com.maguo.loan.cash.flow.entrance.common.service.ProjectInfoService;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import com.maguo.loan.cash.flow.entity.Order;
import com.maguo.loan.cash.flow.enums.RepayMethodEnum;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Objects;
import java.util.Set;

@Service
public class LoanCommonCheckService {
    private static final Logger logger = LoggerFactory.getLogger(LoanCommonService.class);

    @Autowired
    private ProjectInfoService projectInfoService;

    /**
     * 统一校验放款阶段参数
     *
     * @param order 放款信息
     */
    public void checkLoanParameters(Order order , Integer RepaymentType)  {
        logger.info("开始校验放款参数, loanId: {}", order.getId());

        // 获取项目要素
        ProjectInfoDto projectInfoVO = projectInfoService.queryProjectInfo(order.getProjectCode());
        if (Objects.isNull(projectInfoVO)) {
            logger.error("未找到项目要素, projectCode: {}", order.getProjectCode());
            throw new BizException(ResultCode.PROJECT_ELEMENTS_NOT_FOUND);
        }
        //校验还款类型
        checkRepaymentType(order, RepaymentType, projectInfoVO.getElements().getSupportedRepayTypes());

        // 校验借款期限
        checkLoanTerms(order.getApplyPeriods(), projectInfoVO.getElements().getLoanTerms());

        // 校验单笔提现步长
        checkDrawableAmountStep(order.getApplyAmount(), projectInfoVO.getElements().getDrawableAmountStep());

        logger.info("放款参数校验通过, loanId: {}", order.getId());
    }

    /**
     * 校验还款类型
     */
    private void checkRepaymentType(Order order, Integer RepaymentType, RepaymentType supportedRepayTypes) {

        // 1. 检查还款类型是否为空
        if (Objects.isNull(supportedRepayTypes)) {
            throw new BizException(ResultCode.REPAYMENT_TYPE_REQUIRED.getMsg(), ResultCode.REPAYMENT_TYPE_REQUIRED);
        }
        //LVXIN目前未传还款类型 跳过该校验
        if (FlowChannel.LVXIN.equals(order.getFlowChannel())) {
            return;
        }

        // 2. 校验还款类型
        String normalizedRefundMethod;
        if (RepayMethodEnum.EQUAL_INSTALLMENT.getCode().equals(RepaymentType.toString())) {
            normalizedRefundMethod = String.valueOf(RepayMethodEnum.EQUAL_INSTALLMENT);
        } else {
            // 目前只支持值为1的情况，后续可能会有其他值
            throw new BizException("不支持的还款类型", ResultCode.PARAM_ILLEGAL);
        }

        // 3. 检查支持的还款类型列表中是否包含当前还款类型
        String[] supportedTypes = supportedRepayTypes.getCode().split(",");
        boolean isSupported = Arrays.stream(supportedTypes)
            .map(String::trim)
            .anyMatch(type -> type.equals(normalizedRefundMethod));

        if (!isSupported) {
            throw new BizException("不支持的还款类型：" + normalizedRefundMethod, ResultCode.UNSUPPORTED_REPAYMENT_TYPE);
        }
    }

    /**
     * 校验借款期限
     *
     * @param applyTerms   申请的借款期限
     * @param allowedTerms 允许的借款期限 (英文逗号分隔)
     */
    private void checkLoanTerms(Integer applyTerms, String allowedTerms)  {
        if (allowedTerms == null || allowedTerms.isEmpty()) {
            logger.info("未设置允许的借款期限，跳过校验");
            return;
        }

        Set<Integer> allowedTermsSet = new HashSet<>();
        String[] termsArray = allowedTerms.split(",");
        for (String term : termsArray) {
            try {
                allowedTermsSet.add(Integer.parseInt(term.trim()));
            } catch (NumberFormatException e) {
                logger.error("借款期限格式错误: {}", term, e);
                throw new BizException(ResultCode.LOAN_TERMS_FORMAT_ERROR);
            }
        }

        if (!allowedTermsSet.contains(applyTerms)) {
            logger.warn("借款期限不在允许范围内: {}, 允许的期限: {}", applyTerms, allowedTerms);
            throw new BizException(ResultCode.LOAN_TERM_NOT_IN_ALLOWED_RANGE);
        }
    }

    /**
     * 校验单笔提现步长
     *
     * @param amount 提现金额
     * @param step   提现步长
     */
    private void checkDrawableAmountStep(BigDecimal amount, String step) {
        if (step == null || step.isEmpty()) {
            logger.info("未设置单笔提现步长，跳过校验");
            return;
        }

        try {
            BigDecimal stepValue = new BigDecimal(step);
            if (stepValue.compareTo(BigDecimal.ZERO) <= 0) {
                logger.error("单笔提现步长必须大于0: {}", step);
                throw new BizException(ResultCode.DRAWABLE_AMOUNT_STEP_INVALID);
            }

            // 检查金额是否是步长的整数倍
            BigDecimal remainder = amount.remainder(stepValue);
            if (remainder.compareTo(BigDecimal.ZERO) != 0) {
                logger.warn("提现金额不是步长的整数倍: {}, 步长: {}", amount, step);
                throw new BizException(ResultCode.DRAWABLE_AMOUNT_STEP_NOT_MATCH);
            }
        } catch (NumberFormatException e) {
            logger.error("单笔提现步长格式错误: {}", step, e);
            throw new BizException(ResultCode.DRAWABLE_AMOUNT_STEP_FORMAT_ERROR);
        }
    }
}
