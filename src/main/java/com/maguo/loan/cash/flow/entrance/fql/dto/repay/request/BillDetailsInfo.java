package com.maguo.loan.cash.flow.entrance.fql.dto.repay.request;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import java.math.BigDecimal;
import java.util.List;

/**
 * (还款账单明细,如果是提前结清,会有多条)(List结构)(包含在withholdDetail中)
 */
public class BillDetailsInfo {
    /**
     * 账单维度的还款总额（单位：分）
     */
    @NotBlank(message = "账单维度的还款总额不能为空")
    private BigDecimal rpyAmt;
    /**
     * 实还本金（单位：分）
     */
    @NotBlank(message = "实还本金不能为空")
    private BigDecimal rpyPrincipal;
    /**
     * 实还利息（单位：分）
     */
    @NotBlank(message = "实还利息不能为空")
    private BigDecimal rpyFeeAmt;
    /**
     * 实还罚息（单位：分）
     */
    @NotBlank(message = "实还罚息不能为空")
    private BigDecimal rpyMuclt;
    /**
     * 可能会其他类型的金额,用json数组的格式提高扩展性
     * 其他科目金额
     */
    private List<OtherInfo> otherInfo;
    /**
     * 还款期数
     */
    @NotNull(message = "还款期数不能为空")
    private Integer rpyTerm;

    public BigDecimal getRpyAmt() {
        return rpyAmt;
    }

    public void setRpyAmt(BigDecimal rpyAmt) {
        this.rpyAmt = rpyAmt;
    }

    public BigDecimal getRpyPrincipal() {
        return rpyPrincipal;
    }

    public void setRpyPrincipal(BigDecimal rpyPrincipal) {
        this.rpyPrincipal = rpyPrincipal;
    }

    public BigDecimal getRpyFeeAmt() {
        return rpyFeeAmt;
    }

    public void setRpyFeeAmt(BigDecimal rpyFeeAmt) {
        this.rpyFeeAmt = rpyFeeAmt;
    }

    public BigDecimal getRpyMuclt() {
        return rpyMuclt;
    }

    public void setRpyMuclt(BigDecimal rpyMuclt) {
        this.rpyMuclt = rpyMuclt;
    }

    public List<OtherInfo> getOtherInfo() {
        return otherInfo;
    }

    public void setOtherInfo(List<OtherInfo> otherInfo) {
        this.otherInfo = otherInfo;
    }

    public Integer getRpyTerm() {
        return rpyTerm;
    }

    public void setRpyTerm(Integer rpyTerm) {
        this.rpyTerm = rpyTerm;
    }
}
