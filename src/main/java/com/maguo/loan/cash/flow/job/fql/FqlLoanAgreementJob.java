package com.maguo.loan.cash.flow.job.fql;

import com.alibaba.fastjson2.JSON;
import com.jinghang.capital.api.dto.FileType;
import com.jinghang.capital.api.dto.ProcessStatus;
import com.jinghang.capital.api.dto.Product;
import com.jinghang.capital.api.dto.RestResult;
import com.jinghang.capital.api.dto.file.FileDownloadDto;
import com.jinghang.capital.api.dto.file.FileDownloadResultDto;
import com.jinghang.cash.api.dto.ProjectAgreementDto;
import com.jinghang.cash.api.enums.ActiveInactive;
import com.jinghang.common.util.CollectionUtil;
import com.jinghang.common.util.JsonUtil;
import com.maguo.loan.cash.flow.convert.EnumConvert;
import com.maguo.loan.cash.flow.entity.AgreementSignRelation;
import com.maguo.loan.cash.flow.entity.AgreementSignatureRecord;
import com.maguo.loan.cash.flow.entity.Loan;
import com.maguo.loan.cash.flow.entity.Order;
import com.maguo.loan.cash.flow.entity.UserFile;
import com.maguo.loan.cash.flow.entrance.fql.config.FqlConfig;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import com.maguo.loan.cash.flow.enums.LoanStage;
import com.maguo.loan.cash.flow.enums.ProcessState;
import com.maguo.loan.cash.flow.enums.WhetherState;
import com.maguo.loan.cash.flow.job.AbstractJobHandler;
import com.maguo.loan.cash.flow.job.JobParam;
import com.maguo.loan.cash.flow.remote.core.FinLoanFileService;
import com.maguo.loan.cash.flow.remote.manage.ProjectAgreementFeign;
import com.maguo.loan.cash.flow.repository.AgreementSignRelationRepository;
import com.maguo.loan.cash.flow.repository.AgreementSignatureRecordRepository;
import com.maguo.loan.cash.flow.repository.LoanRepository;
import com.maguo.loan.cash.flow.repository.OrderRepository;
import com.maguo.loan.cash.flow.repository.UserFileRepository;
import com.maguo.loan.cash.flow.service.FileService;
import com.maguo.loan.cash.flow.service.WarningService;
import com.maguo.loan.cash.flow.util.SftpUtils;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 借款合同下载
 */
@Component
@JobHandler("fqlLoanAgreementJob")
public class FqlLoanAgreementJob extends AbstractJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(FqlLoanAgreementJob.class);

    private static final String JOB_NAME = "fqlLoanAgreementJob";

    private static final String REPLACE="#assetId#";

    @Autowired
    private SftpUtils sftpUtils;

    @Autowired
    private FqlConfig fqlConfig;

    @Autowired
    private OrderRepository orderRepository;

    @Autowired
    private LoanRepository loanRepository;

    @Autowired
    private FinLoanFileService finLoanFileService;

    @Autowired
    private WarningService warningService;

    @Autowired
    private UserFileRepository userFileRepository;

    @Autowired
    private AgreementSignRelationRepository agreementSignRelationRepository;

    @Value(value = "${oss.bucket.name}")
    private String ossBucket;

    @Autowired
    private FileService fileService;

    @Autowired
    private AgreementSignatureRecordRepository agreementSignatureRecordRepository;

    @Autowired
    private ProjectAgreementFeign projectAgreementFeign;

    @Override
    public void doJob(JobParam jobParam) {
        logger.info("{} jobParam: {}", JOB_NAME, JsonUtil.toJsonString(jobParam));

        LocalDate fileDate = jobParam.getStartDate();
        LocalDate endDate = jobParam.getEndDate();
        if (Objects.isNull(fileDate)) {
            fileDate = LocalDate.now().minusDays(1);
        }
        if (Objects.isNull(endDate)) {
            endDate = fileDate;
        }
        String bizDateStr = fileDate.format(DateTimeFormatter.ofPattern("yyyyMMdd"));

        List<Loan> list;
        if (CollectionUtil.isNotEmpty(jobParam.getLoanIds())) {
            logger.info("fqlLoanAgreementJob loanIds size:{}", jobParam.getLoanIds().size());
            //根据借据id数组查询
            list = loanRepository.findAllById(jobParam.getLoanIds());
        } else if (jobParam.getBankChannel() != null) {
            if (jobParam.getStartDate() == null || jobParam.getEndDate() == null) {
                logger.error("{} 指定资方,startDate和endDate必填",JOB_NAME);
                return;
            }
            logger.info("{} 指定资方:{},开始时间:{},结束时间:{}",JOB_NAME, jobParam.getBankChannel(), jobParam.getStartDate(), jobParam.getEndDate());
            //根据资方、放款时间 查询
            list = loanRepository.findByLoanStateAndBankChannelAndLoanTimeBetweenAndFlowChannel(
                    ProcessState.SUCCEED, jobParam.getBankChannel(), LocalDateTime.of(fileDate, LocalTime.MIN), LocalDateTime.of(endDate, LocalTime.MAX), FlowChannel.FQLQY001);
        } else {
            logger.info("{} 指定开始时间:{},结束时间:{}", JOB_NAME,fileDate, endDate);
            list = loanRepository.findByLoanStateAndLoanTimeBetweenAndFlowChannel(
                    ProcessState.SUCCEED, LocalDateTime.of(fileDate, LocalTime.MIN), LocalDateTime.of(endDate, LocalTime.MAX), FlowChannel.FQLQY001);
        }

        if (CollectionUtils.isEmpty(list)) {
            logger.info("{} 放款成功的借据为空",JOB_NAME);
            return;
        }
        //1.获取所有放款单据对应的project-code
        //2.根据project-code和 is_return_to_flow=true 获取对应配置信息 并且把文件类型跟文件名称组装好 使用map
        //3.根据放款单据和配置表中的文件类型下载对应的协议
        //4.根据风险id查询AgreementSignatureRecord表流量签署的协议
        //5.根据用户记录表中的文件类型 取配置表中对应的合同名称，组装文件名称
        //6.上传分期乐sftp
        Set<String> projectCodes = list.stream().map(Loan::getProjectCode).collect(Collectors.toSet());
        //回传流量的协议配置
        List<ProjectAgreementDto> agreementToFlowDtos = projectAgreementFeign.getByReturnStatus(projectCodes.stream().toList(), ActiveInactive.Y.getCode(), "");
        List<ProjectAgreementDto> agreementToCapitalDtos = agreementToFlowDtos.stream()
            .filter(dto -> "资方".equals(dto.getTemplateOwner()))
            .toList();
        //所有文件name和对应的文件合同名称
        Map<String, ProjectAgreementDto> filenameFlowMaps = agreementToFlowDtos.stream()
            .collect(Collectors.toMap(agreementDto -> agreementDto.getProjectCode()+agreementDto.getContractTemplateType().name(),
                Function.identity()
            ));
        Map<String, ProjectAgreementDto> filenameCapitalMaps = agreementToCapitalDtos.stream()
            .collect(Collectors.toMap(agreementDto -> agreementDto.getProjectCode()+agreementDto.getContractTemplateType().name(),
                Function.identity()
            ));
        //开始拉取
        logger.info("借款合同下载,loan size:{}", list.size());
        for (Loan loan : list) {
            logger.info("开始获取协议loanId:{}", loan.getId());
            List<UserFile> userFiles = agreementSignRelationRepository.queryUserFiles(Optional.ofNullable(loan.getLoanRecordId()).orElse(loan.getId()));
            Map<String, String> ossMap = new HashMap<>();
            if (CollectionUtil.isNotEmpty(userFiles)) {
                ossMap = userFiles.stream().collect(Collectors.toMap(UserFile::getOssKey, UserFile::getOssBucket, (k1, k2) -> k2));
            }
            List<FileType> fileTypes  = filenameCapitalMaps.keySet().stream().map(filetype->{
                String replace = filetype.replace(loan.getProjectCode(), "");
                return FileType.valueOf(replace);}).toList();
            for (FileType fileType : fileTypes) {
                try {
                    //下载借款合同
                    FileDownloadDto fileDownloadDto = new FileDownloadDto();
                    fileDownloadDto.setLoanId(loan.getLoanNo());
                    fileDownloadDto.setLoanOrderId(Optional.ofNullable(loan.getLoanRecordId()).orElse(loan.getId()));
                    fileDownloadDto.setType(fileType);
                    fileDownloadDto.setProduct(Product.ZC_CASH);
                    fileDownloadDto.setBankChannel(loan.getBankChannel());
                    RestResult<FileDownloadResultDto> restResult = finLoanFileService.download(fileDownloadDto);
                    logger.info("下载借款合同文件,入参{},出参{}", JSON.toJSONString(fileDownloadDto), JSON.toJSONString(restResult));
                    if (!restResult.isSuccess()) {
                        //warningService.warn("下载借款合同文件异常:" + JSON.toJSONString(fileDownloadDto),
                        //    msg -> logger.error("下借款合同文件异常:{}", JSON.toJSONString(restResult)));
                        logger.error("下借款合同文件异常,loanId:{}", loan.getId());
                        continue;
                    }
                    if (Objects.isNull(restResult.getData()) || StringUtils.isAnyBlank(restResult.getData().getOssBucket(),
                            restResult.getData().getOssPath())) {
                        logger.info("合同文件不存在,loanId:{},fileType:{}", loan.getId(), fileType);
                        continue;
                    }
                    // 过滤已经下载的文件
                    if (ossMap.containsKey(restResult.getData().getOssPath()) && ossMap.get(restResult.getData().getOssPath())
                            .equals(restResult.getData().getOssBucket())) {
                        logger.info("借据:{},跳过:{}文件下载", loan.getId(), fileType);
                        continue;
                    }
                    //新增影像协议文件
                    saveUserFile(loan, fileType, restResult);


                } catch (Exception e) {
                    logger.error("借款合同下载异常loanId:{}", loan.getId(), e);
                }
            }
            Order order = orderRepository.findOrderById(loan.getOrderId());
            // 这里根据riskId查询AgreementSignatureRecord表流量签署的协议
            List<AgreementSignatureRecord> agreementList = agreementSignatureRecordRepository.findByRiskIdAndSignState(order.getRiskId(), ProcessState.SUCCEED);
            if (agreementList != null && !agreementList.isEmpty()) {
                for (AgreementSignatureRecord record : agreementList) {
                    fileService.getOssFile(ossBucket, record.getCommonOssUrl(), inputStream -> {
                        try {
                            /**
                             * 个人敏感信息授权书 sensitive_#assetId#.pdf
                             * 委托扣款授权书 guarantee_#assetId#.pdf
                             * 综合授权书-担保（四合一） consolidated_#assetId#.pdf
                             * 承诺书 undertaking_#assetId#.pdf
                             */
                            String filename =filenameFlowMaps.get(loan.getProjectCode()+record.getFileType().name()).getFlowContractName().replace(REPLACE, order.getOuterOrderId());
                            //分期乐固定地址
                            sftpUtils.fqlUploadStreamToSftp(inputStream, filename, fqlConfig.getAgreementSftpPath(bizDateStr));
                        } catch (Exception e) {
                            logger.error("协议文件上传分期乐sftp失败:", e);
                            throw new RuntimeException(e);
                        }
                    });
                }
            }

            logger.info("开始获取资金系统协议参数:{}", loan.getId());
            // 获取资金系统签署完成与资方返回的协议
            List<UserFile> userFileList = userFileRepository.findByUserIdAndLoanNo(order.getUserId(), loan.getId());
            logger.info("获取资金系统签署完成与资方返回的协议:{}", JsonUtil.toJsonString(userFileList));
            if (userFileList != null && !userFileList.isEmpty()) {
                for (UserFile userFile : userFileList) {
                    if (userFile.getFileType().equals(com.maguo.loan.cash.flow.enums.FileType.CREDIT_SETTLE_VOUCHER_FILE)) {
                        continue;
                    }
                    fileService.getOssFile(userFile.getOssBucket(), userFile.getOssKey(), inputStream -> {
                        try {
                            /**
                             * 综合授权书 consolidated2_#assetId#.pdf
                             * 数字证书使用授权协议 credit_#assetId#.pdf
                             * 个人客户扣款授权书 deduction_#assetId#.pdf
                             * 借款合同（已盖章） loan_contract_#assetId#.pdf
                             */
                            //获取资金方合同名称规则
                            String filename =filenameCapitalMaps.get(loan.getProjectCode()+userFile.getFileType().name()).getCapitalContractName().replace(REPLACE, order.getOuterOrderId());
                            //分期乐的
                            sftpUtils.fqlUploadStreamToSftp(inputStream, filename, fqlConfig.getAgreementSftpPath(bizDateStr));
                        } catch (Exception e) {
                            logger.error("协议文件上传分期乐sftp失败:", e);
                            throw new RuntimeException(e);
                        }
                    });
                }
            }


        }
        logger.info("{} finished",JOB_NAME);
    }

    private void saveUserFile(Loan loan, FileType fileType, RestResult<FileDownloadResultDto> restResult) {
        FileDownloadResultDto resultData = restResult.getData();
        UserFile userFile = new UserFile();
        userFile.setUserId(loan.getUserId());
        LoanStage loanStage = getLoanStage(fileType);
        userFile.setLoanStage(loanStage);
        userFile.setLoanNo(loan.getId());
        com.maguo.loan.cash.flow.enums.FileType cashFileType = EnumConvert.INSTANCE.toCoreApi(fileType);
        userFile.setFileType(cashFileType);
        String fileName = StringUtils.isNotBlank(resultData.getFileName())
                ? resultData.getFileName() : Optional.ofNullable(cashFileType).map(com.maguo.loan.cash.flow.enums.FileType::getDesc).orElse("");
        userFile.setFileName("资金-" + fileName);
        userFile.setOssBucket(resultData.getOssBucket());
        userFile.setOssKey(resultData.getOssPath());
        userFile.setSignFinal(ProcessStatus.SUCCESS == resultData.getFileStatus() ? WhetherState.Y : WhetherState.N);
        userFileRepository.save(userFile);

        AgreementSignRelation agreementSignRelation = new AgreementSignRelation();
        agreementSignRelation.setLoanStage(loanStage);
        agreementSignRelation.setSignApplyId(userFile.getId());
        agreementSignRelation.setUserId(loan.getUserId());
        agreementSignRelation.setOrderId(loan.getOrderId());
        if (loanStage == LoanStage.CREDIT) {
            agreementSignRelation.setRelatedId(loan.getCreditId());
        } else {
            agreementSignRelation.setRelatedId(Optional.ofNullable(loan.getLoanRecordId()).orElse(loan.getId()));
        }
        agreementSignRelationRepository.save(agreementSignRelation);
    }

    private LoanStage getLoanStage(FileType fileType) {
        return switch (fileType) {
            case CREDIT_APPLY, PERSONAL_CREDIT_AUTHORIZATION_LETTER, PROMISE_NOT_STUDENT,
                 PERSONAL_INFORMATION_QUERY_LETTER -> LoanStage.CREDIT;
            default -> LoanStage.LOAN;
        };
    }
}
