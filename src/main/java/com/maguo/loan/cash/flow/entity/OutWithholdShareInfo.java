package com.maguo.loan.cash.flow.entity;

import com.maguo.loan.cash.flow.enums.WhetherState;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;

import java.math.BigDecimal;

/**
 * <p>
 * 外部代扣分账信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-07
 */
@Entity
@Table(name = "out_withhold_share_info")
public class OutWithholdShareInfo extends BaseEntity {


    /**
     * 外部代扣id
     */
    private String outWithholdFlowId;

    /**
     * 商户号
     */
    private String merchantNo;

    /**
     * 商户名称
     */
    private String merchantName;

    /**
     * 本金
     */
    private BigDecimal principal;

    /**
     * 利息
     */
    private BigDecimal interest;

    /**
     * 违约金
     */
    private BigDecimal breach;

    /**
     * 罚息
     */
    private BigDecimal penalty;

    /**
     * 融担费
     */
    private BigDecimal guarantee;

    /**
     * 咨询费
     */
    private BigDecimal consult;

    /**
     * 分账总额
     */
    private BigDecimal amount;

    /**
     * 是否需要通知确认
     */
    @Enumerated(EnumType.STRING)
    private WhetherState merchantConfirm;

    @Override
    protected String prefix() {
        return "OSI";
    }

    public String getOutWithholdFlowId() {
        return outWithholdFlowId;
    }

    public void setOutWithholdFlowId(String outWithholdFlowId) {
        this.outWithholdFlowId = outWithholdFlowId;
    }

    public String getMerchantNo() {
        return merchantNo;
    }

    public void setMerchantNo(String merchantNo) {
        this.merchantNo = merchantNo;
    }

    public String getMerchantName() {
        return merchantName;
    }

    public void setMerchantName(String merchantName) {
        this.merchantName = merchantName;
    }

    public BigDecimal getPrincipal() {
        return principal;
    }

    public void setPrincipal(BigDecimal principal) {
        this.principal = principal;
    }

    public BigDecimal getInterest() {
        return interest;
    }

    public void setInterest(BigDecimal interest) {
        this.interest = interest;
    }

    public BigDecimal getBreach() {
        return breach;
    }

    public void setBreach(BigDecimal breach) {
        this.breach = breach;
    }

    public BigDecimal getPenalty() {
        return penalty;
    }

    public void setPenalty(BigDecimal penalty) {
        this.penalty = penalty;
    }

    public BigDecimal getGuarantee() {
        return guarantee;
    }

    public void setGuarantee(BigDecimal guarantee) {
        this.guarantee = guarantee;
    }

    public BigDecimal getConsult() {
        return consult;
    }

    public void setConsult(BigDecimal consult) {
        this.consult = consult;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public WhetherState getMerchantConfirm() {
        return merchantConfirm;
    }

    public void setMerchantConfirm(WhetherState merchantConfirm) {
        this.merchantConfirm = merchantConfirm;
    }
}
