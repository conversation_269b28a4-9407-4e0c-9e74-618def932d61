package com.maguo.loan.cash.flow.entrance.fql.dto.res;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jinghang.common.util.DateUtil;
import com.maguo.loan.cash.flow.entrance.ppd.dto.CommonResult;

import java.math.BigDecimal;
import java.time.LocalDateTime;

public class FqlLoanQueryResponse extends CommonResult {

    @JsonFormat(pattern = DateUtil.NEW_PATTERN)
    private LocalDateTime cashDate;

    private BigDecimal loanAmt;

    public LocalDateTime getCashDate() {
        return cashDate;
    }

    public void setCashDate(LocalDateTime cashDate) {
        this.cashDate = cashDate;
    }

    public BigDecimal getLoanAmt() {
        return loanAmt;
    }

    public void setLoanAmt(BigDecimal loanAmt) {
        this.loanAmt = loanAmt;
    }
}
