package com.maguo.loan.cash.flow.config;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;

/**
 * 灵活的LocalDateTime反序列化器
 * 支持多种日期格式，解决与Cash-Manage模块的兼容性问题
 * 
 * 支持的格式：
 * - ISO格式：yyyy-MM-dd'T'HH:mm:ss
 * - 空格格式：yyyy-MM-dd HH:mm:ss (Cash-Manage模块使用)
 * 
 * <AUTHOR>
 * @date 2025-08-22
 */
public class FlexibleLocalDateTimeDeserializer extends JsonDeserializer<LocalDateTime> {
    
    private static final DateTimeFormatter ISO_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss");
    private static final DateTimeFormatter SPACE_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Override
    public LocalDateTime deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
        String value = p.getValueAsString();
        if (value == null || value.trim().isEmpty()) {
            return null;
        }
        
        try {
            // 首先尝试ISO格式
            return LocalDateTime.parse(value, ISO_FORMAT);
        } catch (DateTimeParseException e1) {
            try {
                // 然后尝试空格格式（Cash-Manage使用的格式）
                return LocalDateTime.parse(value, SPACE_FORMAT);
            } catch (DateTimeParseException e2) {
                throw new IOException("Failed to parse LocalDateTime: " + value + 
                    ". Supported formats: yyyy-MM-dd'T'HH:mm:ss, yyyy-MM-dd HH:mm:ss");
            }
        }
    }
}
