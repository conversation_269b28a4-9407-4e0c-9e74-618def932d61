package com.maguo.loan.cash.flow.entity.common;

import com.jinghang.common.util.StringUtil;
import com.maguo.loan.cash.flow.entity.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.time.LocalDateTime;

/**
 * 产品项目代码映射
 *
 * @Author: Lior
 * @CreateTime: 2025/8/18 11:57
 */
@Getter
@Setter
@Entity
@Table(name = "project_product_mapping")
public class ProjectProductMapping extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1234567890123456789L;

    /**
     * 产品编码
     */
    @Column(name = "product_code")
    private String productCode;

    /**
     * 项目类型名称
     */
    @Column(name = "project_code")
    private String projectCode;


    @Override
    protected String prefix() {
        return "PPM";
    }

    @Override
    public void prePersist() {
        if (StringUtil.isBlank(super.getId())) {
            setId(genId());
        }
        super.setCreatedBy("sys");
        super.setCreatedTime(LocalDateTime.now());
    }
}
