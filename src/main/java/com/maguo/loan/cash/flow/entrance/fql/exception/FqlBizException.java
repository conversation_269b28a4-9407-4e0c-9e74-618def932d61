package com.maguo.loan.cash.flow.entrance.fql.exception;



import java.io.Serial;

/**
 * 自定异常类
 */
public class FqlBizException extends RuntimeException {

    @Serial
    private static final long serialVersionUID = 1431155259402142272L;

    private final FqlResultCode resultCode;

    public FqlBizException(String message) {
        super(message);
        this.resultCode = FqlResultCode.SYSTEM_ERROR;
    }

    public FqlBizException(FqlResultCode resultCode) {
        super(resultCode.getMsg());
        this.resultCode = resultCode;
    }

    public FqlBizException(String message, FqlResultCode resultCode) {
        super(message);
        this.resultCode = resultCode;
    }

    public FqlResultCode getResultCode() {
        return resultCode;
    }

}
