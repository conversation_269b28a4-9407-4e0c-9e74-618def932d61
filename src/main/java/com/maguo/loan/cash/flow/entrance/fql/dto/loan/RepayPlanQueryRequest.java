package com.maguo.loan.cash.flow.entrance.fql.dto.loan;

import jakarta.validation.constraints.NotBlank;

/*
 * @Description: 分期乐还款计划查询请求
 * @Author: abai
 * @Date: 2025-8-11 下午 05:27
 */
public class RepayPlanQueryRequest {

    /**
     * 合作方代码
     */
    @NotBlank(message = "合作方代码不能为空")
    private String partnerCode;

    /**
     * 支用申请编号
     */
    @NotBlank(message = "支用申请编号不能为空")
    private String applyId;

    /**
     * 资方放款编号
     */
    @NotBlank(message = "资方放款编号不能为空")
    private String capitalLoanNo;

    public String getPartnerCode() {
        return partnerCode;
    }

    public void setPartnerCode(String partnerCode) {
        this.partnerCode = partnerCode;
    }

    public String getApplyId() {
        return applyId;
    }

    public void setApplyId(String applyId) {
        this.applyId = applyId;
    }

    public String getCapitalLoanNo() {
        return capitalLoanNo;
    }

    public void setCapitalLoanNo(String capitalLoanNo) {
        this.capitalLoanNo = capitalLoanNo;
    }
}
