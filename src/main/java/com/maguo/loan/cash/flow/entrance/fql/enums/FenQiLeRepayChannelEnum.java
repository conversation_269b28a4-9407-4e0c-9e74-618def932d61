package com.maguo.loan.cash.flow.entrance.fql.enums;

/**
 * <AUTHOR>
 * @Description 还款路径:1-清分 2-代扣 状态
 * @Date 2025/8/11 15:20
 * @Version v1.0
 **/
public enum FenQiLeRepayChannelEnum {

    SORTING("1", "清分"),
    WITHHOLDING("2", "代扣");

    private String code;

    private String desc;

    FenQiLeRepayChannelEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

}
