package com.maguo.loan.cash.flow.entrance.fql.dto.repay;

/**
 * @ClassName RealReturnNoticeResponse
 * <AUTHOR>
 * @Description 实还通知响应参数
 * @Date 2025/8/11 14:37
 * @Version v1.0
 **/
public class FenQiLeRealReturnNoticeResponse {

    /**
     * 还款状态
     * 1、接口通知成功(仅代表收到乐信侧请求,具体的还款结果以查询接口为准)
     * 2、接口通知失败(代表未收到乐信侧请求,乐信侧会重复发起还款申请)
     */
    private Integer notifyStatus;
    /**
     * 还款描述
     * 失败时，需给出的错误描述
     */
    private String msg;

    public Integer getNotifyStatus() {
        return notifyStatus;
    }

    public void setNotifyStatus(Integer notifyStatus) {
        this.notifyStatus = notifyStatus;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public static FenQiLeRealReturnNoticeResponse fail(String msg) {
        FenQiLeRealReturnNoticeResponse result = new FenQiLeRealReturnNoticeResponse();
        result.setNotifyStatus(2);
        result.setMsg(msg);
        return result;
    }
}
