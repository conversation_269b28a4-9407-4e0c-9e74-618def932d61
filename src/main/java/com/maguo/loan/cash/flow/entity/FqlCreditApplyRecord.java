package com.maguo.loan.cash.flow.entity;

import com.maguo.loan.cash.flow.enums.EquityRecipient;
import com.maguo.loan.cash.flow.enums.IsIncludingEquity;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;

import java.math.BigDecimal;

/**
 * 分期乐授信申请
 */
@Entity
@Table(name = "fql_credit_apply_record")
public class FqlCreditApplyRecord extends BaseEntity{

    /**
     * 授信申请编号
     */
    private String creditApplyId;
    /**
     * 合作方代码
     */
    private String partnerCode;
    /**
     * 授信金额，单位元
     */
    private BigDecimal creditAmount;
    /**
     * 申请人姓名
     */
    private String name;
    /**
     * 婚姻状况，0-未婚，1-已婚，3-离异，4-未知，5-丧偶
     */
    private String maritalStatus;
    /**
     * 年龄
     */
    private Integer age;
    /**
     * 性别，0-未知，1-男，2-女
     */
    private String sex;
    /**
     * 学历， 0未知，1本科，2大专，3硕士，4博士，7初中，10大专(老)，11中专，12高中，13小学
     */
    private String educationLevel;
    /**
     * 证件类型，1: 身份证（暂时只支持该选项）
     */
    private String identiType;
    /**
     * 证件号码
     */
    private String identiNo;
    /**
     * 身份证有效期起始日期，YYYYMMDD
     */
    private String idCardValidDate;

    /**
     * 身份证有效期结束日期，YYYYMMDD 如果是长期-20991231
     */
    private String idCardExpireDate;
    /**
     * 身份证地址
     */
    private String idAddr;
    /**
     * 身份证签发机关
     */
    private String issuedAgency;
    /**
     * 出生日期，yyyy-MM-dd
     */
    private String birthday;
    /**
     * 国籍
     */
    private String nationality;
    /**
     * 民族
     */
    private String nation;
    /**
     * 客户手机号
     */
    private String mobileNo;
    /**
     * 用户银行卡号
     */
    private String userBankCardNo;
    /**
     * 开户行编码
     */
    private String paymentBankCode;
    /**
     * 期数
     */
    private Integer loanTerm;
    /**
     * 对客展示利率
     */
    private BigDecimal yearInterestRate;
    /**
     * 单位名称
     */
    private String companyName;
    /**
     * 第一联系人姓名
     */
    private String contactName;
    /**
     * 第一联系人手机
     */
    private String contactMobile;
    /**
     * 第一联系人关系
     */
    private String contactRel;
    /**
     * 居住地址
     */
    private String livingAddress;
    /**
     * 客户职业
     */
    private String userOccupation;
    /**
     * 客户行业
     */
    private String userIndustryCategory;
    /**
     * 影像信息
     */
    private String uploadInfo;
    /**
     * 是否含权益
     * Y:含权益：N:不含权益
     */
    @Enumerated(EnumType.STRING)
    private IsIncludingEquity isIncludingEquity;

    /**
     * 权益收取方
     * O：外部,I：内部（默认O：外部）
     */
    @Enumerated(EnumType.STRING)
    private EquityRecipient equityRecipient;

    public String getCreditApplyId() {
        return creditApplyId;
    }

    public void setCreditApplyId(String creditApplyId) {
        this.creditApplyId = creditApplyId;
    }

    public String getPartnerCode() {
        return partnerCode;
    }

    public void setPartnerCode(String partnerCode) {
        this.partnerCode = partnerCode;
    }

    public BigDecimal getCreditAmount() {
        return creditAmount;
    }

    public void setCreditAmount(BigDecimal creditAmount) {
        this.creditAmount = creditAmount;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getMaritalStatus() {
        return maritalStatus;
    }

    public void setMaritalStatus(String maritalStatus) {
        this.maritalStatus = maritalStatus;
    }

    public Integer getAge() {
        return age;
    }

    public void setAge(Integer age) {
        this.age = age;
    }

    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public String getEducationLevel() {
        return educationLevel;
    }

    public void setEducationLevel(String educationLevel) {
        this.educationLevel = educationLevel;
    }

    public String getIdentiType() {
        return identiType;
    }

    public void setIdentiType(String identiType) {
        this.identiType = identiType;
    }

    public String getIdentiNo() {
        return identiNo;
    }

    public void setIdentiNo(String identiNo) {
        this.identiNo = identiNo;
    }

    public String getIdCardValidDate() {
        return idCardValidDate;
    }

    public void setIdCardValidDate(String idCardValidDate) {
        this.idCardValidDate = idCardValidDate;
    }

    public String getIdCardExpireDate() {
        return idCardExpireDate;
    }

    public void setIdCardExpireDate(String idCardExpireDate) {
        this.idCardExpireDate = idCardExpireDate;
    }

    public String getIdAddr() {
        return idAddr;
    }

    public void setIdAddr(String idAddr) {
        this.idAddr = idAddr;
    }

    public String getIssuedAgency() {
        return issuedAgency;
    }

    public void setIssuedAgency(String issuedAgency) {
        this.issuedAgency = issuedAgency;
    }

    public String getBirthday() {
        return birthday;
    }

    public void setBirthday(String birthday) {
        this.birthday = birthday;
    }

    public String getNationality() {
        return nationality;
    }

    public void setNationality(String nationality) {
        this.nationality = nationality;
    }

    public String getNation() {
        return nation;
    }

    public void setNation(String nation) {
        this.nation = nation;
    }

    public String getMobileNo() {
        return mobileNo;
    }

    public void setMobileNo(String mobileNo) {
        this.mobileNo = mobileNo;
    }

    public String getUserBankCardNo() {
        return userBankCardNo;
    }

    public void setUserBankCardNo(String userBankCardNo) {
        this.userBankCardNo = userBankCardNo;
    }

    public String getPaymentBankCode() {
        return paymentBankCode;
    }

    public void setPaymentBankCode(String paymentBankCode) {
        this.paymentBankCode = paymentBankCode;
    }

    public Integer getLoanTerm() {
        return loanTerm;
    }

    public void setLoanTerm(Integer loanTerm) {
        this.loanTerm = loanTerm;
    }

    public BigDecimal getYearInterestRate() {
        return yearInterestRate;
    }

    public void setYearInterestRate(BigDecimal yearInterestRate) {
        this.yearInterestRate = yearInterestRate;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getContactName() {
        return contactName;
    }

    public void setContactName(String contactName) {
        this.contactName = contactName;
    }

    public String getContactMobile() {
        return contactMobile;
    }

    public void setContactMobile(String contactMobile) {
        this.contactMobile = contactMobile;
    }

    public String getContactRel() {
        return contactRel;
    }

    public void setContactRel(String contactRel) {
        this.contactRel = contactRel;
    }

    public String getLivingAddress() {
        return livingAddress;
    }

    public void setLivingAddress(String livingAddress) {
        this.livingAddress = livingAddress;
    }

    public String getUserOccupation() {
        return userOccupation;
    }

    public void setUserOccupation(String userOccupation) {
        this.userOccupation = userOccupation;
    }

    public String getUserIndustryCategory() {
        return userIndustryCategory;
    }

    public void setUserIndustryCategory(String userIndustryCategory) {
        this.userIndustryCategory = userIndustryCategory;
    }

    public String getUploadInfo() {
        return uploadInfo;
    }

    public void setUploadInfo(String uploadInfo) {
        this.uploadInfo = uploadInfo;
    }

    public IsIncludingEquity getIsIncludingEquity() {
        return isIncludingEquity;
    }

    public void setIsIncludingEquity(IsIncludingEquity isIncludingEquity) {
        this.isIncludingEquity = isIncludingEquity;
    }

    public EquityRecipient getEquityRecipient() {
        return equityRecipient;
    }

    public void setEquityRecipient(EquityRecipient equityRecipient) {
        this.equityRecipient = equityRecipient;
    }
}
