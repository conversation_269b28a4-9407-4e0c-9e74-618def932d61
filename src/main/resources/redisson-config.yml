singleServerConfig:
  # Redis 服务器地址，从 Apollo 配置中心获取
  address: "redis://127.0.0.1:6379"
  
  # 连接池配置
  connectionMinimumIdleSize: 10
  connectionPoolSize: 64
  
  # 订阅连接池配置
  subscriptionConnectionMinimumIdleSize: 1
  subscriptionConnectionPoolSize: 50
  
  # 关键配置：禁用从节点连接，避免 WAIT 命令
  slaveConnectionMinimumIdleSize: 0
  slaveConnectionPoolSize: 0
  
  # 超时配置
  idleConnectionTimeout: 10000
  connectTimeout: 10000
  timeout: 3000
  retryAttempts: 3
  retryInterval: 1500
  
  # 密码配置（如果需要）
  # password: "your_redis_password"
  
  # 数据库选择
  database: 0

# 编解码器
codec: !<org.redisson.codec.JsonJacksonCodec> {}

# 传输模式
transportMode: "NIO"
