<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jinghang.cash.modules.project.mapper.ProjectElementsMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.jinghang.cash.modules.project.domain.ProjectElements">
        <id column="id" property="id" jdbcType="VARCHAR"/>
        <result column="project_code" property="projectCode" jdbcType="VARCHAR"/>
        <result column="drawable_amount_range" property="drawableAmountRange" jdbcType="VARCHAR"/>
        <result column="drawable_amount_step" property="drawableAmountStep" jdbcType="VARCHAR"/>
        <result column="credit_dark_hours" property="creditDarkHours" jdbcType="VARCHAR"/>
        <result column="loan_dark_hours" property="loanDarkHours" jdbcType="VARCHAR"/>
        <result column="repay_dark_hours" property="repayDarkHours" jdbcType="VARCHAR"/>
        <result column="funding_credit_dark_hours" property="fundingCreditDarkHours" jdbcType="VARCHAR"/>
        <result column="funding_loan_dark_hours" property="fundingLoanDarkHours" jdbcType="VARCHAR"/>
        <result column="funding_repay_dark_hours" property="fundingRepayDarkHours" jdbcType="VARCHAR"/>
        <result column="daily_credit_limit" property="dailyCreditLimit" jdbcType="DECIMAL"/>
        <result column="daily_loan_limit" property="dailyLoanLimit" jdbcType="DECIMAL"/>
        <result column="credit_lock_days" property="creditLockDays" jdbcType="INTEGER"/>
        <result column="loan_lock_days" property="loanLockDays" jdbcType="INTEGER"/>
        <result column="customer_interest_rate" property="customerInterestRate" jdbcType="VARCHAR"/>
        <result column="funding_interest_rate" property="fundingInterestRate" jdbcType="VARCHAR"/>
        <result column="age_range" property="ageRange" jdbcType="VARCHAR"/>
        <result column="supported_repay_types" property="supportedRepayTypes" jdbcType="VARCHAR"/>
        <result column="loan_terms" property="loanTerms" jdbcType="VARCHAR"/>
        <result column="capital_route" property="capitalRoute" jdbcType="VARCHAR"/>
        <result column="project_duration_type" property="projectDurationType" jdbcType="VARCHAR"/>
        <result column="temp_start_time" property="tempStartTime" jdbcType="TIMESTAMP"/>
        <result column="temp_end_time" property="tempEndTime" jdbcType="TIMESTAMP"/>
        <result column="enabled" property="enabled" jdbcType="VARCHAR"/>
        <result column="grace_next" property="graceNext" jdbcType="VARCHAR"/>
        <result column="revision" property="revision" jdbcType="INTEGER"/>
        <result column="created_by" property="createdBy" jdbcType="VARCHAR"/>
        <result column="created_time" property="createdTime" jdbcType="TIMESTAMP"/>
        <result column="updated_by" property="updatedBy" jdbcType="VARCHAR"/>
        <result column="updated_time" property="updatedTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, project_code, drawable_amount_range, drawable_amount_step, credit_dark_hours,
        loan_dark_hours, repay_dark_hours, funding_credit_dark_hours, funding_loan_dark_hours,
        funding_repay_dark_hours, daily_credit_limit, daily_loan_limit, credit_lock_days,
        loan_lock_days, customer_interest_rate, funding_interest_rate, age_range,
        supported_repay_types, loan_terms, capital_route, project_duration_type,
        temp_start_time, temp_end_time, enabled, grace_next, revision,
        created_by, created_time, updated_by, updated_time
    </sql>

    <!-- 根据项目编码查询项目要素 -->
    <select id="selectByProjectCode" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM project_elements
        WHERE project_code = #{projectCode,jdbcType=VARCHAR}
        LIMIT 1
    </select>

    <!-- 根据项目编码查询启用状态的项目要素，按时效类型排序（临时优先） -->
    <select id="selectByProjectCodeAndEnabledOrderByProjectDurationTypeDesc" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM project_elements
        WHERE project_code = #{projectCode,jdbcType=VARCHAR}
        AND enabled = #{enabled,jdbcType=VARCHAR}
        ORDER BY project_duration_type DESC
    </select>

    <!-- 根据项目编码查询有效的临时项目要素 -->
    <select id="selectValidTemporaryElements" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM project_elements
        WHERE project_code = #{projectCode,jdbcType=VARCHAR}
        AND enabled = #{enabled,jdbcType=VARCHAR}
        AND project_duration_type = #{durationType,jdbcType=VARCHAR}
        AND temp_start_time &lt;= #{currentTime,jdbcType=TIMESTAMP}
        AND temp_end_time &gt;= #{currentTime,jdbcType=TIMESTAMP}
        LIMIT 1
    </select>

    <!-- 根据项目编码查询长期项目要素 -->
    <select id="selectByProjectCodeAndEnabledAndProjectDurationType" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM project_elements
        WHERE project_code = #{projectCode,jdbcType=VARCHAR}
        AND enabled = #{enabled,jdbcType=VARCHAR}
        AND project_duration_type = #{durationType,jdbcType=VARCHAR}
        LIMIT 1
    </select>
    <select id="findAll" resultType="com.jinghang.cash.modules.project.domain.ProjectElements">

    </select>

</mapper>
