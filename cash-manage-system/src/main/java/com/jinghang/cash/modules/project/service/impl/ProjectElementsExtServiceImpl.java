/*
*  Copyright 2019-2025 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.jinghang.cash.modules.project.service.impl;

import com.jinghang.cash.modules.project.domain.ProjectElementsExt;
import com.jinghang.cash.utils.PageResult;
import lombok.RequiredArgsConstructor;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jinghang.cash.modules.project.service.ProjectElementsExtService;
import com.jinghang.cash.modules.project.domain.dto.ProjectElementsExtQueryCriteria;
import com.jinghang.cash.modules.project.mapper.ProjectElementsExtMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;
import java.util.Map;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.LinkedHashMap;

/**
* @description 服务实现
* <AUTHOR>
* @date 2025-08-25
**/
@Service
@RequiredArgsConstructor
public class ProjectElementsExtServiceImpl extends ServiceImpl<ProjectElementsExtMapper, ProjectElementsExt> implements ProjectElementsExtService {

    private final ProjectElementsExtMapper projectElementsExtMapper;

    @Override
    public PageResult<ProjectElementsExt> queryAll(ProjectElementsExtQueryCriteria criteria, Page<Object> page){
        //return PageUtil.toPage(projectElementsExtMapper.findAll(criteria, page));
        return null;
    }

    @Override
    public List<ProjectElementsExt> queryAll(ProjectElementsExtQueryCriteria criteria){
        return projectElementsExtMapper.findAll(criteria);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(ProjectElementsExt resources) {
        projectElementsExtMapper.insert(resources);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(ProjectElementsExt resources) {
        ProjectElementsExt projectElementsExt = getById(resources.getId());
        projectElementsExt.copy(resources);
        projectElementsExtMapper.updateById(projectElementsExt);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteAll(List<String> ids) {
        projectElementsExtMapper.deleteBatchIds(ids);
    }

    @Override
    public void download(List<ProjectElementsExt> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (ProjectElementsExt projectElementsExt : all) {
            Map<String,Object> map = new LinkedHashMap<>();
            map.put("项目要素主键ID", projectElementsExt.getParentId());
            map.put("关联的项目唯一编码", projectElementsExt.getProjectCode());
            map.put("年利率基数(天) (如 360或365)", projectElementsExt.getInterestDaysBasis());
            map.put("是否支持线下跨日还款", projectElementsExt.getAllowCrossDayRepay());
            map.put("风控模型渠道", projectElementsExt.getRiskModelChannel());
            map.put("放款支付渠道", projectElementsExt.getLoanPaymentChannel());
            map.put("扣款绑卡渠道", projectElementsExt.getDeductionBindCardChannel());
            map.put("扣款商户号", projectElementsExt.getDeductionMerchantCode());
            map.put("签章渠道", projectElementsExt.getSignChannel());
            map.put("逾期短信发送方", projectElementsExt.getOverdueSmsSender());
            map.put("短信渠道", projectElementsExt.getSmsChannel());
            map.put("逾期宽限期类型 (SQ:首期, MQ:每期)", projectElementsExt.getGracePeriodType());
            map.put("逾期宽限期(天)", projectElementsExt.getGracePeriodDays());
            map.put("节假日是否顺延", projectElementsExt.getHolidayPostpone());
            map.put("征信查询方", projectElementsExt.getCreditQueryParty());
            map.put("征信上报方", projectElementsExt.getCreditReportSender());
            map.put("催收方", projectElementsExt.getCollectionParty());
            map.put("是否推送催收数据", projectElementsExt.getPushCollectionData());
            map.put("是否推送客诉数据", projectElementsExt.getPushKsData());
            map.put("是否支持催收减免", projectElementsExt.getAllowCollectionWaiver());
            map.put("备注", projectElementsExt.getRemark());
            map.put("乐观锁", projectElementsExt.getRevision());
            map.put("创建人", projectElementsExt.getCreatedBy());
            map.put("创建时间", projectElementsExt.getCreatedTime());
            map.put("更新人", projectElementsExt.getUpdatedBy());
            map.put("更新时间", projectElementsExt.getUpdatedTime());
            list.add(map);
        }
        //FileUtil.downloadExcel(list, response);
    }
}